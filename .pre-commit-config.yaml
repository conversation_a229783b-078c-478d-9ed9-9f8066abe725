default_stages: [commit]
fail_fast: true

repos:
  - repo: https://github.com/ambv/black
    rev: 22.3.0
    hooks:
      - id: black

  - repo: https://github.com/commitizen-tools/commitizen
    rev: v2.24.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.2.0
    hooks:
      - id: check-added-large-files
      - id: trailing-whitespace
        stages: [commit]
      - id: end-of-file-fixer
        stages: [commit]
      - id: debug-statements

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-added-large-files
        stages: [ commit ]
      - id: trailing-whitespace
        stages: [ commit ]
      - id: end-of-file-fixer
        stages: [ commit ]
