# from re import match
#
# match match := match('([^@]+)@.*', '<EMAIL>').group(1):
#     case 'alice':
#         print('alice')
#     case 'bob':
#         print('bob')
#     case _:
#         print('other')
#
#
# import keyword
#
# print(keyword.kwlist)
# print(keyword.softkwlist)
#
# PROMPT = "\N{snake}}"
#
# match input(PROMPT):
#     case 'snake':
#         print('snake')
#     case 'python':
#         print('python')
#     case _:
#         print('other')
#
#
# # 表情符号
# star = "\N{SPARKLES}"
# smile = "\N{grinning face with smiling eyes}"
# celebrate = "\N{party popper}"
# cat = "\N{cat face}"
# dog = "\N{dog face}"
# pizza = "\N{slice of pizza}"
# beer = "\N{beer mug}"
# cake = "\N{shortcake}"
#
# # 特殊符号
# sparkles = "\N{sparkles}"
# fire = "\N{fire}"
# lightning = "\N{high voltage sign}"
# rainbow = "\N{rainbow}"
# new_moon = "\N{new moon symbol}"
# first_quarter_moon = "\N{first quarter moon symbol}"
# full_moon = "\N{full moon symbol}"
#
# # 数学符号
# sum_symbol = "\N{n-ary summation}"
# integral = "\N{integral}"
# infinity = "\N{infinity}"
# approx_equal = "\N{almost equal to}"
# not_equal = "\N{not equal to}"
# less_than_or_equal = "\N{less-than or equal to}"
# greater_than_or_equal = "\N{greater-than or equal to}"
#
# # 其他符号
# books = "\N{books}"
# open_book = "\N{open book}"
# # notebook = "\N{spiral notepad}"
# # loudspeaker = "\N{loudspeaker}"
# # telephone = "\N{black telephone}"
# # email = "\N{envelope}"
# # earth = "\N{earth globe asia-australia}"
#
# # 打印示例
# print(f"Star: {star}")
# print(f"Smile: {smile}")
# print(f"Celebrate: {celebrate}")
# print(f"Cat: {cat}")
# print(f"Dog: {dog}")
# print(f"Pizza: {pizza}")
# print(f"Beer: {beer}")
# print(f"Cake: {cake}")
#
# print(f"Sparkles: {sparkles}")
# print(f"Fire: {fire}")
# print(f"Lightning: {lightning}")
# print(f"Rainbow: {rainbow}")
# print(f"New Moon: {new_moon}")
# print(f"First Quarter Moon: {first_quarter_moon}")
# print(f"Full Moon: {full_moon}")
#
# print(f"Sum Symbol: {sum_symbol}")
# print(f"Integral: {integral}")
# print(f"Infinity: {infinity}")
# print(f"Approx Equal: {approx_equal}")
# print(f"Not Equal: {not_equal}")
# print(f"Less Than or Equal: {less_than_or_equal}")
# print(f"Greater Than or Equal: {greater_than_or_equal}")
#
# print(f"Books: {books}")
# print(f"Open Book: {open_book}")
# print(f"Notebook: {notebook}")
# print(f"Loudspeaker: {loudspeaker}")
# print(f"Telephone: {telephone}")
# print(f"Email: {email}")
# print(f"Earth: {earth}")
