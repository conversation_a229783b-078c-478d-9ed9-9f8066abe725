import collections


class Solution:
    def test(self, nums, target):
        if not nums:
            return -1

        n = len(nums)
        left = 0
        right = n - 1

        while left <= right:
            mid = (right + left) // 2
            if nums[mid] == target:
                return mid

            if nums[left] <= nums[mid]:
                if nums[left] <= target <= nums[mid]:
                    right = mid - 1
                else:
                    left = mid + 1
            else:
                if nums[mid] <= target <= nums[right]:
                    left = mid + 1
                else:
                    right = mid - 1

        return -1


if __name__ == "__main__":
    solution = Solution()
    nums = [4, 5, 6, 7, 0, 1, 2]
    target = 0
    print(solution.test(nums, target))
    print(solution.test([4, 5, 6, 7, 0, 1, 2], 3))
