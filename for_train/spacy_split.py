from spacy.language import Language
import spacy
import re


def is_roman_numeral(text):
    # 匹配罗马数字的模式
    pattern = r'^(?=[MDCLXVI])M{0,3}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})\.?$'
    return bool(re.match(pattern, text.strip('.').upper()))


nlp = spacy.load("en_core_web_sm")

# 自定义组件来处理罗马数字
# @Language.component("custom_stop_words")
# def custom_stop_words(doc):
#     for token in doc:
#         if is_roman_numeral(token.text):
#             token.is_stop = False  # 确保罗马数字不被视为停用词
#     return doc


@Language.component("set_custom_boundaries")
def set_custom_boundaries(doc):
    for token in doc[:-1]:
        if is_roman_numeral(token.text):
            # doc[token.i + 1].is_sent_start = False
            doc[token.i + 1].is_sent_end = False
    return doc


nlp.add_pipe("set_custom_boundaries", before="parser")

# text = "As at the date of the AGM, the total number of issued Shares was 480,000,000 Shares, all of which was the total number of Shares entitling the holders to attend and vote for or against the Proposed Resolutions at the AGM. There were no restrictions on any Shareholders to cast votes on any of the Proposed Resolutions at the AGM. There were no Shares entitling the Shareholders to attend and abstain from voting in favour of any of the Proposed Resolutions at the AGM as set out in Rule 13.40 of the Listing Rules. No Shareholder was required under the Listing Rules to abstain from voting on the Proposed Resolutions at the AGM. None of the Shareholders have indicated in the Circular that they intended to vote against or to abstain from voting on any Proposed Resolutions at the AGM."
text = "IV. BIOGRAPHIES OF DIRECTORS, SUPERVISORS AND SENIOR MANAGEMENT (CONT’D)."
with nlp.disable_pipes("senter"):
    doc = nlp(text)
    sentences = [sent.text.strip() for sent in doc.sents]
    for i in sentences:
        print(i)
