import asyncio
from blacksheep import Application, Response, StreamedContent
import uvicorn

app = Application()
app.use_cors(
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)


async def event_generator():
    for _ in range(5):
        # 每隔 1 秒返回数据
        data = "data: 高老师总能分享出好东西\r\n\r\n".encode("utf-8")
        yield data
        await asyncio.sleep(1)


@app.router.get("/")
async def sse():
    return Response(
        200,
        content=StreamedContent(b"text/event-stream", event_generator),
    )


if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=9999)
