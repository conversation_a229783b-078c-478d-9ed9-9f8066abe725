import PyPDF2
import pandas as pd
import re
from typing import List, Dict, Any


class HKEXRuleExtractor:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path

    def extract_text_from_pages(self, start_page: int, end_page: int) -> str:
        """Extract text from specified page range (1-indexed)"""
        with open(self.pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""

            # Convert to 0-indexed for PyPDF2
            start_idx = start_page - 1
            end_idx = min(end_page, len(pdf_reader.pages))

            for page_num in range(start_idx, end_idx):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"

        return text

    def parse_rules(self, text: str) -> List[Dict[str, str]]:
        """Parse rule structure from text"""
        rules = []

        # Find all main sections (A.1, A.2, B.1, etc.)
        main_section_pattern = r'([A-F])\.(\d+)\s+([^\n]*?)(?=\nPrinciple)'
        main_sections = re.finditer(main_section_pattern, text, re.DOTALL | re.MULTILINE)

        for main_match in main_sections:
            section_letter = main_match.group(1)
            section_num = main_match.group(2)
            section_title = self._clean_text(main_match.group(3))
            rule = f"{section_letter}.{section_num}"

            # Find the principle for this section
            principle_start = main_match.end()
            principle_match = re.search(r'Principle\s*\n(.*?)(?=Code Provisions|Recommended Best Practices|[A-F]\.\d+|$)',
                                        text[principle_start:], re.DOTALL)
            principle = ""
            if principle_match:
                principle = self._clean_text(principle_match.group(1))

            # Find section boundaries
            section_start = main_match.start()
            next_section_match = re.search(r'\n([A-F]\.\d+)\s+[^\n]', text[main_match.end():])
            if next_section_match:
                section_end = main_match.end() + next_section_match.start()
            else:
                section_end = len(text)

            section_content = text[section_start:section_end]

            # Find all sub-rules in this section (both in Code Provisions and Recommended Best Practices)
            sub_rule_pattern = rf'({re.escape(section_letter)})\.({re.escape(section_num)})\.(\d+)\s+(.*?)(?={re.escape(section_letter)}\.{re.escape(section_num)}\.\d+|\n[A-F]\.\d+\.\d+|\n[A-F]\.\d+\s+[^.]|\Z)'

            sub_rules = re.finditer(sub_rule_pattern, section_content, re.DOTALL)

            for sub_match in sub_rules:
                sub_rule_num = sub_match.group(3)
                sub_rule = f"{rule}.{sub_rule_num}"
                code_provision_text = self._clean_text(sub_match.group(4))

                rules.append({
                    'rule': rule,
                    'section_title': section_title,
                    'Principle': principle,
                    'sub_rule': sub_rule,
                    'code_provisions': code_provision_text
                })

        return rules

    def _clean_text(self, text: str) -> str:
        """Clean extracted text while preserving meaningful line breaks"""
        # First, normalize multiple spaces and tabs to single spaces
        text = re.sub(r'[ \t]+', ' ', text)

        # Preserve line breaks that are followed by meaningful content like:
        # - List items starting with (a), (b), (c), etc.
        # - New sentences or clauses
        # But remove line breaks that are just splitting words

        # Replace single line breaks with spaces, but keep double line breaks
        text = re.sub(r'(?<!\n)\n(?!\n)', ' ', text)

        # Now preserve line breaks before list items like "(a)", "(b)", etc.
        text = re.sub(r'\s+\(([a-z])\)', r'\n(\1)', text)

        # Preserve line breaks before numbered items like "1.", "2.", etc.
        text = re.sub(r'\s+(\d+\.)', r'\n\1', text)

        # Preserve line breaks before Notes
        text = re.sub(r'\s+(AC\d+\s*–\s*\d+\s*Note:)', r'\n\n\1', text)

        # Clean up multiple consecutive line breaks
        text = re.sub(r'\n{3,}', '\n\n', text)

        # Remove leading/trailing whitespace
        text = text.strip()

        return text

    def extract_and_export(self, output_file: str = "hkex_rules.xlsx"):
        """Main function to extract rules and export to Excel"""
        print("Extracting text from PDF pages 12-35...")
        text = self.extract_text_from_pages(57  , 67)

        print("Parsing rules...")
        rules = self.parse_rules(text)

        if not rules:
            print("No rules found. Let me check the text structure...")
            # Save extracted text for debugging
            with open('extracted_text_debug.txt', 'w', encoding='utf-8') as f:
                f.write(text)
            print("Saved extracted text to 'extracted_text_debug.txt' for analysis")
            return

        print(f"Found {len(rules)} rules. Exporting to Excel...")

        # Create DataFrame
        df = pd.DataFrame(rules)

        # Export to Excel
        df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"Rules exported to {output_file}")

        return rules


if __name__ == "__main__":
    # pdf_path = "/Users/<USER>/workspace/xx/data/HKEX4476_3828_VER37460.pdf"
    pdf_path = "/Users/<USER>/workspace/xx/data/cp202406cc_old_rule.pdf"
    extractor = HKEXRuleExtractor(pdf_path)
    extractor.extract_and_export(output_file="/Users/<USER>/Downloads/hkex_rules_old.xlsx")