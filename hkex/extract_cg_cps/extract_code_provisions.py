"""
从PDF中提取Code Provisions数据并导出到Excel

功能：
1. ��取指定PDF文件的第57-67页
2. 提取rule B.1.3-F.1.25的code provisions
3. 导出到Excel，包含三列：rule、sub_rule、code_provisions
"""

import re
import pymupdf
from openpyxl import Workbook


def extract_text_from_pdf(pdf_path, start_page, end_page):
    """
    从PDF中提取指定页面的文本

    Args:
        pdf_path: PDF文件路径
        start_page: 起始页码（从1开始）
        end_page: 结束页码（包含）

    Returns:
        str: 提取的文本内容
    """
    doc = pymupdf.open(pdf_path)
    text = ""

    # PyMuPDF的页码从0开始，所以需要减1
    for page_num in range(start_page - 1, end_page):
        page = doc[page_num]
        text += page.get_text()

    doc.close()
    return text


def parse_code_provisions(text):
    """
    解析文本，提取rule、sub_rule和code_provisions

    Args:
        text: PDF提取的文本内容

    Returns:
        list: 包含字典的列表，每个字典包含rule、sub_rule、code_provisions
    """
    results = []

    # 正则表达式匹配rule标题，格式如：B.1 Board composition, succession and evaluation
    rule_pattern = r'^([A-F]\.\d+)\s+(.+?)$'

    # 正则表达式匹配sub_rule，支持多段编号（3段或4段）：
    # 1. 单独一行的编号：B.1.3 或 F.2.11.2
    # 2. 编号和文本在同一行：B.1.3 The board should... 或 F.2.11.2 For each...
    # 使用 (?:\.\d+)+ 来匹配一个或多个 .数字 的模式
    sub_rule_pattern_alone = r'^([A-F]\.\d+(?:\.\d+)+)$'
    sub_rule_pattern_with_text = r'^([A-F]\.\d+(?:\.\d+)+)\s+(.+)$'

    lines = text.split('\n')

    # 第一步：收集所有rule信息
    rules_dict = {}  # 存储 rule_code -> rule_full_name 的映射
    for line in lines:
        line = line.strip()
        rule_match = re.match(rule_pattern, line)
        if rule_match:
            rule_code = rule_match.group(1)  # 如 "B.1"
            rule_name = rule_match.group(2)  # 如 "Board composition, succession and evaluation"
            rules_dict[rule_code] = f"{rule_code} {rule_name}"

    # 第二步：提取sub_rule和code_provisions
    current_sub_rule = None
    code_provisions = []
    in_extractable_section = False  # 标记是否在Code Provisions或Recommended Best Practices部分
    current_rule_section = None  # 当前所在的rule section（如B.1, F.2等）

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # 检查是否遇到新的rule section（如B.1, B.2, C.1等）
        rule_match = re.match(rule_pattern, line)
        if rule_match:
            new_rule_section = rule_match.group(1)  # 如 "B.1", "F.2"

            # 如果rule section发生变化，且当前有sub_rule在收集，则保存当前sub_rule
            if current_rule_section and new_rule_section != current_rule_section:
                if current_sub_rule and code_provisions and in_extractable_section:
                    sub_rule_prefix = '.'.join(current_sub_rule.split('.')[:2])
                    matched_rule = rules_dict.get(sub_rule_prefix, "")
                    results.append({
                        'rule': matched_rule,
                        'sub_rule': current_sub_rule,
                        'code_provisions': ' '.join(code_provisions).strip()
                    })
                    current_sub_rule = None
                    code_provisions = []

            current_rule_section = new_rule_section

        # 检查是否进入Code Provisions或Recommended Best Practices部分
        if re.match(r'^Code Provision', line):
            in_extractable_section = True
            i += 1
            continue
        elif re.match(r'^Recommended Best Practice', line):
            # 保存当前sub_rule（如果有的话）
            if current_sub_rule and code_provisions and in_extractable_section:
                sub_rule_prefix = '.'.join(current_sub_rule.split('.')[:2])
                matched_rule = rules_dict.get(sub_rule_prefix, "")
                results.append({
                    'rule': matched_rule,
                    'sub_rule': current_sub_rule,
                    'code_provisions': ' '.join(code_provisions).strip()
                })
                current_sub_rule = None
                code_provisions = []
            # 继续保持在可提取区域
            in_extractable_section = True
            i += 1
            continue

        # 在Code Provisions或Recommended Best Practices部分处理sub_rule
        if in_extractable_section:
            # 检查是否是sub_rule（支持两种格式）
            sub_rule_match_alone = re.match(sub_rule_pattern_alone, line)
            sub_rule_match_with_text = re.match(sub_rule_pattern_with_text, line)

            if sub_rule_match_alone or sub_rule_match_with_text:
                # 确定是哪种格式
                if sub_rule_match_with_text:
                    # 格式：B.3.1 The nomination committee should...
                    sub_rule_candidate = sub_rule_match_with_text.group(1)
                    first_line_text = sub_rule_match_with_text.group(2)
                else:
                    # 格式：B.3.5（单独一行）
                    sub_rule_candidate = sub_rule_match_alone.group(1)
                    first_line_text = None

                sub_rule_prefix = '.'.join(sub_rule_candidate.split('.')[:2])

                # 验证：sub_rule的前缀应该在rules_dict中存在
                # （可以是当前rule section，也可以是之前的rule section，特别是在Recommended Best Practices部分）
                if sub_rule_prefix not in rules_dict:
                    i += 1
                    continue

                # 保存之前的sub_rule和code_provisions
                if current_sub_rule and code_provisions:
                    # 根据sub_rule的前缀找到对应的rule
                    prev_sub_rule_prefix = '.'.join(current_sub_rule.split('.')[:2])
                    matched_rule = rules_dict.get(prev_sub_rule_prefix, "")

                    results.append({
                        'rule': matched_rule,
                        'sub_rule': current_sub_rule,
                        'code_provisions': ' '.join(code_provisions).strip()
                    })

                # 开始新的sub_rule
                current_sub_rule = sub_rule_candidate
                code_provisions = []

                # 如果编号和文本在同一行，将文本作为第一行内容
                if first_line_text:
                    code_provisions.append(first_line_text)

                i += 1
                continue

            # 如果当前有sub_rule，则收集code_provisions内容
            if current_sub_rule and line:
                # 跳过一些特殊行（如页码、标题等）
                skip_patterns = [
                    r'^\d+$',  # 纯数字
                    r'^Code Provision',  # Code Provisions标题
                    r'^Recommended Best Practice',  # 最佳实践标题
                    r'^…+$',  # 省略号
                    r'^III-\d+$',  # 页码标记如 III-9
                ]

                should_skip = False
                for pattern in skip_patterns:
                    if re.match(pattern, line):
                        should_skip = True
                        break

                # 如果这行看起来像是rule标题，也跳过
                if re.match(rule_pattern, line):
                    should_skip = True

                if not should_skip:
                    code_provisions.append(line)

        i += 1

    # 保存最后一个sub_rule（如果它在可提取部分）
    if current_sub_rule and code_provisions and in_extractable_section:
        sub_rule_prefix = '.'.join(current_sub_rule.split('.')[:2])
        matched_rule = rules_dict.get(sub_rule_prefix, "")

        results.append({
            'rule': matched_rule,
            'sub_rule': current_sub_rule,
            'code_provisions': ' '.join(code_provisions).strip()
        })

    return results


def export_to_excel(data, output_path):
    """
    将数据导出到Excel文件

    Args:
        data: 包含字典的列表
        output_path: 输出Excel文件路径
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "Code Provisions"

    # 写入表头
    ws.append(['rule', 'sub_rule', 'code_provisions'])

    # 写入数据
    for row in data:
        ws.append([row['rule'], row['sub_rule'], row['code_provisions']])

    # 调整列宽
    ws.column_dimensions['A'].width = 50
    ws.column_dimensions['B'].width = 15
    ws.column_dimensions['C'].width = 100

    wb.save(output_path)
    print(f"数据已导出到: {output_path}")


def main():
    """主函数"""
    pdf_path = "/Users/<USER>/workspace/xx/hkex/extract_cg_cps/cp202406cc_old_rule.pdf"
    output_path = "/Users/<USER>/workspace/xx/hkex/extract_cg_cps/code_provisions.xlsx"

    print("开始提取PDF内容...")
    text = extract_text_from_pdf(pdf_path, 57, 67)

    # 保存提取的文本用于调试
    debug_path = "/Users/<USER>/workspace/xx/hkex/extract_cg_cps/extracted_text_debug.txt"
    with open(debug_path, 'w', encoding='utf-8') as f:
        f.write(text)
    print(f"原始文本已保存到: {debug_path}")

    print("解析code provisions...")
    data = parse_code_provisions(text)
    print(f"共提取 {len(data)} 条记录")

    # 打印前几条记录供检查
    print("\n前3条记录预览：")
    for i, item in enumerate(data[:3]):
        print(f"\n记录 {i+1}:")
        print(f"  Rule: {item['rule']}")
        print(f"  Sub-rule: {item['sub_rule']}")
        print(f"  Code Provisions: {item['code_provisions'][:100]}...")

    print("\n导出到Excel...")
    export_to_excel(data, output_path)
    print("完成！")


if __name__ == "__main__":
    main()
