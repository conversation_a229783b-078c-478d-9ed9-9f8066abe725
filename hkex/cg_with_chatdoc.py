import asyncio
import json
import logging
import os
import re
import time
from pathlib import Path
from typing import Dict, Optional

import duckdb
import httpx

logger = logging.getLogger(__name__)

FILE_PATH = '/Users/<USER>/Downloads/annual_reports'
CHATDOC_URL = 'https://chatdocdemo.test.paodingai.com'
AUTHORIZATION_TOKEN = 'sk-'
DB_PATH = 'chatdoc_results.db'

# Regex pattern to extract stock_code and report_year from filename
# Expected format: 00018-ORIENTAL ENTERPRISE HOLDINGS LIMITED-2025.pdf
FILENAME_PATTERN = re.compile(r'(\d{5})-.*?-(\d{4})\.pdf$')

# Question template for CG analysis
CG_QUESTION = """hkex监管部门要求上市公司必须披露以下信息
```
董事會的組成（按董事類別劃分），當中包括主席、執行董事、非執行董事、獨立
非執行董事及首席獨立非執行董事（如有）的姓名，以及每名董事的任期及當前委
任期間；
```
请帮我提取这些信息， 并将其归类到下面四个 rule 中， 并给每个 rule 确定一个分类
```
Comply： 正常披露
Explain： 没有正常披露但是有解释为什么不披露或者解释不需要披露
No Disclosure：没有披露也没有解释
```

rules:
```
1. B(a) -1 – Board Composition
2. B(a) -2 – Lead INED (if any)
3. B(a) -3 – Length of tenure
4. B(a) -4 – Current period of appointment
```
"""


def init_database():
    """Initialize DuckDB database with required tables"""
    conn = duckdb.connect(DB_PATH)
    conn.execute("CREATE SEQUENCE IF NOT EXISTS seq_uploads_id START 1;")
    conn.execute("CREATE SEQUENCE IF NOT EXISTS seq_data_id START 1;")
    # Create uploads table
    conn.execute(
        """
        CREATE TABLE IF NOT EXISTS uploads (
        id BIGINT PRIMARY KEY DEFAULT nextval('seq_uploads_id'),  -- 核心自增逻辑            upload_id VARCHAR,
            file_name VARCHAR,
            stock_code VARCHAR,
            report_year VARCHAR,
            status INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    )

    # Create answers table
    conn.execute(
        """
        CREATE TABLE IF NOT EXISTS answers (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_data_id'),  -- 核心自增逻辑
            upload_id VARCHAR,
            question TEXT,
            answer TEXT,
            model_type VARCHAR,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    )

    conn.close()


def extract_file_info(filename: str) -> Optional[Dict[str, str]]:
    """Extract stock_code and report_year from filename"""
    match = FILENAME_PATTERN.search(filename)
    if match:
        return {'stock_code': match.group(1), 'report_year': match.group(2)}
    return None


async def upload(file_path: str) -> Optional[Dict]:
    """
    Upload a PDF file to ChatDoc API

    Args:
        file_path: Path to the PDF file to upload

    Returns:
        Dict containing upload response data or None if failed
    """
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return None

    filename = os.path.basename(file_path)
    url = f"{CHATDOC_URL}/api/external/v3/documents/upload"

    headers = {'accept': 'application/json', 'Authorization': f'Bearer {AUTHORIZATION_TOKEN}'}

    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            with open(file_path, 'rb') as file:
                files = {'file': (filename, file, 'application/pdf')}
                response = await client.post(url, headers=headers, files=files)
                response.raise_for_status()

                result = response.json()
                if result.get('status') == 'ok':
                    logger.info(f"Successfully uploaded {filename}, upload_id: {result['data']['id']}")
                    return result['data']
                else:
                    logger.error(f"Upload failed for {filename}: {result}")
                    return None

    except Exception as e:
        logger.error(f"Error uploading {filename}: {e}")
        return None


async def get_parse_status(upload_id: str) -> Optional[Dict]:
    """
    Get document parsing status from ChatDoc API

    Args:
        upload_id: The upload ID returned from upload API

    Returns:
        Dict containing status data or None if failed

    Status codes:
        1 (UN_PARSED): Document uploaded but not parsed
        300 (ELEMENT_PARSED): Document parsing successful
        < 0: Error during parsing
    """
    url = f"{CHATDOC_URL}/api/external/v3/documents/{upload_id}"

    headers = {'accept': 'application/json', 'Authorization': f'Bearer {AUTHORIZATION_TOKEN}'}

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, headers=headers)
            response.raise_for_status()

            result = response.json()
            if result.get('status') == 'ok':
                return result['data']
            else:
                logger.error(f"Failed to get status for {upload_id}: {result}")
                return None

    except Exception as e:
        logger.error(f"Error getting status for {upload_id}: {e}")
        return None


async def complete(
    upload_id: str, question: str = CG_QUESTION, model_type: str = "deepseek/deepseek-r1-distill-llama-70b"
) -> Optional[Dict]:
    """
    Submit a question to ChatDoc API for document analysis

    Args:
        upload_id: The upload ID of the document
        question: The question to ask (defaults to CG analysis question)
        model_type: The model to use for analysis

    Returns:
        Dict containing the answer or None if failed
    """
    url = f"{CHATDOC_URL}/api/external/v3/questions"

    headers = {
        'accept': 'application/json',
        'Authorization': f'Bearer {AUTHORIZATION_TOKEN}',
        'Content-Type': 'application/json',
    }

    payload = {"question": question, "model_type": model_type, "stream": "false", "upload_id": upload_id}

    try:
        async with httpx.AsyncClient(timeout=120.0) as client:  # Longer timeout for LLM processing
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()

            result = response.json()
            logger.info(f"Successfully got answer for {upload_id}")
            return result

    except Exception as e:
        logger.error(f"Error getting answer for {upload_id}: {e}")
        return None


def save_upload_record(upload_data: Dict, file_info: Dict, filename: str):
    """Save upload record to DuckDB"""
    conn = duckdb.connect(DB_PATH)
    try:
        # Get the next available ID
        result = conn.execute("SELECT COALESCE(MAX(id), 0) + 1 FROM uploads").fetchone()
        next_id = result[0] if result else 1

        conn.execute(
            """
            INSERT INTO uploads (id, upload_id, file_name, stock_code, report_year, status)
            VALUES (?, ?, ?, ?, ?, ?)
        """,
            [
                next_id,
                upload_data['id'],
                filename,
                file_info['stock_code'],
                file_info['report_year'],
                upload_data['status'],
            ],
        )
        logger.info(f"Saved upload record for {filename}")
    except Exception as e:
        logger.error(f"Error saving upload record: {e}")
    finally:
        conn.close()


def update_upload_status(upload_id: str, status: int):
    """Update upload status in DuckDB"""
    conn = duckdb.connect(DB_PATH)
    try:
        conn.execute(
            """
            UPDATE uploads
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE upload_id = ?
        """,
            [status, upload_id],
        )
        logger.info(f"Updated status for {upload_id} to {status}")
    except Exception as e:
        logger.error(f"Error updating status: {e}")
    finally:
        conn.close()


def save_answer_record(upload_id: str, question: str, answer: Dict, model_type: str):
    """Save answer record to DuckDB"""
    conn = duckdb.connect(DB_PATH)
    try:
        # Get the next available ID
        result = conn.execute("SELECT COALESCE(MAX(id), 0) + 1 FROM answers").fetchone()
        next_id = result[0] if result else 1

        conn.execute(
            """
            INSERT INTO answers (id, upload_id, question, answer, model_type)
            VALUES (?, ?, ?, ?, ?)
        """,
            [next_id, upload_id, question, json.dumps(answer, ensure_ascii=False), model_type],
        )
        logger.info(f"Saved answer record for {upload_id}")
    except Exception as e:
        logger.error(f"Error saving answer record: {e}")
    finally:
        conn.close()


async def wait_for_parsing(upload_id: str, max_wait_time: int = 1800) -> bool:
    """
    Wait for document parsing to complete

    Args:
        upload_id: The upload ID to monitor
        max_wait_time: Maximum time to wait in seconds (default 30 minutes)

    Returns:
        True if parsing completed successfully, False otherwise
    """
    logger.info(f"Waiting for parsing completion for {upload_id}")

    # Initial wait of 3 minutes
    await asyncio.sleep(180)

    start_time = time.time()
    while time.time() - start_time < max_wait_time:
        status_data = await get_parse_status(upload_id)
        if not status_data:
            logger.error(f"Failed to get status for {upload_id}")
            return False

        status = status_data.get('status')
        logger.info(f"Current status for {upload_id}: {status}")

        if status == 300:  # ELEMENT_PARSED
            logger.info(f"Parsing completed for {upload_id}")
            update_upload_status(upload_id, status)
            return True
        elif status < 0:  # Error status
            logger.error(f"Parsing failed for {upload_id} with status {status}")
            update_upload_status(upload_id, status)
            return False

        # Wait 10 seconds before next check
        await asyncio.sleep(10)

    logger.error(f"Timeout waiting for parsing completion for {upload_id}")
    return False


async def main():
    """
    Main function to process all PDF files in FILE_PATH:
    1. Upload each file to ChatDoc
    2. Wait for parsing completion
    3. Submit questions and save answers
    """
    # Initialize database
    init_database()

    # Get all PDF files from the directory
    file_path = Path(FILE_PATH)
    if not file_path.exists():
        logger.error(f"Directory not found: {FILE_PATH}")
        return

    pdf_files = list(file_path.glob("*.pdf"))
    logger.info(f"Found {len(pdf_files)} PDF files to process")

    for pdf_file in pdf_files[1:]:
        filename = pdf_file.name
        logger.info(f"Processing file: {filename}")

        # Extract file information
        file_info = extract_file_info(filename)
        if not file_info:
            logger.warning(f"Could not extract stock_code and report_year from {filename}, skipping")
            continue

        logger.info(f"Extracted info: stock_code={file_info['stock_code']}, report_year={file_info['report_year']}")

        # Upload file
        upload_data = await upload(str(pdf_file))
        if not upload_data:
            logger.error(f"Failed to upload {filename}, skipping")
            continue

        upload_id = upload_data['id']

        # Save upload record
        save_upload_record(upload_data, file_info, filename)

        # Wait for parsing completion
        if not await wait_for_parsing(upload_id):
            logger.error(f"Parsing failed or timed out for {filename}, skipping question submission")
            continue

        # Submit question and get answer
        answer_data = await complete(upload_id)
        if answer_data:
            save_answer_record(upload_id, CG_QUESTION, answer_data, "deepseek/deepseek-r1-distill-llama-70b")
            logger.info(f"Successfully processed {filename}")
        else:
            logger.error(f"Failed to get answer for {filename}")

    logger.info("Processing completed for all files")


if __name__ == '__main__':
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    asyncio.run(main())
