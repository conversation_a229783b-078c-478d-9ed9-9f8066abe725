#!/bin/bash

# GitLab Copy 自动重试脚本
# 用于处理 429 错误（API 频率限制）并自动重试

# 配置参数
MAX_RETRIES=10          # 最大重试次数
BASE_DELAY=60           # 基础延迟时间（秒）
MAX_DELAY=300           # 最大延迟时间（秒）
COMMAND="./gitlab-copy -y config.yml"  # 要执行的命令
LOG_FILE="gitlab-copy-retry.log"       # 日志文件

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# 打印带颜色的消息
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查是否存在 429 错误
check_429_error() {
    local output="$1"
    if echo "$output" | grep -q "429" && echo "$output" | grep -q "This endpoint has been requested too many times"; then
        return 0  # 找到 429 错误
    else
        return 1  # 没有找到 429 错误
    fi
}

# 计算延迟时间（指数退避）
calculate_delay() {
    local attempt=$1
    local delay=$((BASE_DELAY * (2 ** (attempt - 1))))
    if [ $delay -gt $MAX_DELAY ]; then
        delay=$MAX_DELAY
    fi
    echo $delay
}

# 主函数
main() {
    print_colored "$BLUE" "GitLab Copy 自动重试脚本启动"
    log "INFO" "开始执行 GitLab Copy 命令: $COMMAND"
    log "INFO" "最大重试次数: $MAX_RETRIES"

    local attempt=1

    while [ $attempt -le $MAX_RETRIES ]; do
        print_colored "$BLUE" "\n=== 第 $attempt 次尝试 ==="
        log "INFO" "第 $attempt 次尝试执行命令"

        # 执行命令并捕获输出
        local output
        local exit_code

        output=$(eval "$COMMAND" 2>&1)
        exit_code=$?

        # 检查命令是否成功执行
        if [ $exit_code -eq 0 ]; then
            print_colored "$GREEN" "✅ 命令执行成功！"
            log "INFO" "命令执行成功，退出码: $exit_code"
            echo "$output"
            exit 0
        fi

        # 检查是否是 429 错误
        if check_429_error "$output"; then
            print_colored "$YELLOW" "⚠️  检测到 429 错误（API 频率限制）"
            log "WARN" "检测到 429 错误，准备重试"

            if [ $attempt -lt $MAX_RETRIES ]; then
                local delay=$(calculate_delay $attempt)
                print_colored "$YELLOW" "等待 $delay 秒后进行第 $((attempt + 1)) 次重试..."
                log "INFO" "等待 $delay 秒后重试"

                # 倒计时显示
                for ((i=delay; i>0; i--)); do
                    printf "\r等待中... %d 秒" $i
                    sleep 1
                done
                printf "\r等待完成!     \n"

                attempt=$((attempt + 1))
            else
                print_colored "$RED" "❌ 已达到最大重试次数 ($MAX_RETRIES)，放弃重试"
                log "ERROR" "已达到最大重试次数，命令执行失败"
                echo "最后一次的错误输出:"
                echo "$output"
                exit 1
            fi
        else
            # 其他类型的错误，不重试
            print_colored "$RED" "❌ 命令执行失败（非 429 错误）"
            log "ERROR" "命令执行失败，退出码: $exit_code，错误类型: 非429错误"
            echo "错误输出:"
            echo "$output"
            exit $exit_code
        fi
    done
}

# 信号处理
trap 'print_colored "$RED" "\n脚本被中断"; log "INFO" "脚本被用户中断"; exit 130' INT TERM

# 检查必要的命令是否存在
if [ ! -f "./gitlab-copy" ]; then
    print_colored "$RED" "错误: 找不到 gitlab-copy 可执行文件"
    log "ERROR" "找不到 gitlab-copy 可执行文件"
    exit 1
fi

if [ ! -f "config.yml" ]; then
    print_colored "$RED" "错误: 找不到 config.yml 配置文件"
    log "ERROR" "找不到 config.yml 配置文件"
    exit 1
fi

# 执行主函数
main
