import csv
import functools
import gzip
import logging
import os
import re
import time
from datetime import datetime
from enum import unique, Enum
from pathlib import Path
from typing import Union, Iterator, Tu<PERSON>, Any

from filetype import filetype


def _self_path():
    if "__file__" in globals():
        return __file__


project_root = os.path.dirname(os.path.dirname(os.path.abspath(_self_path())))


DATA_PATH = Path(project_root) / "data"


class DateTimeUtil:
    @classmethod
    def str2timestamp(cls, time_str, pattern="%Y/%m/%d %H:%M:%S"):
        date_time = datetime.strptime(time_str, pattern)
        return int(datetime.timestamp(date_time))

    @classmethod
    def timestamp2str(cls, time_stamp, pattern="%Y/%m/%d %H:%M:%S"):
        date_time = datetime.fromtimestamp(time_stamp)
        return datetime.strftime(date_time, pattern)


# 文件
def write_file(
    path,
    data,
    open_pack="open",
    mode="wb",
    write_method=lambda obj, data: obj.write(data),
):
    _open = gzip.open if open_pack == "gzip" else open
    try:
        with _open(path, mode) as fp_obj:
            write_method(fp_obj, data)
    except:
        os.remove(path)
        raise
    return path


def read_file(path, open_pack="open", mode="rb", read_method=lambda obj: obj.read()):
    _open = gzip.open if open_pack == "gzip" else open
    with _open(path, mode) as file_obj:
        data = read_method(file_obj)
    return data


def csv_reader(csv_path, skip_rows=1):
    with open(csv_path, "r", newline="", encoding="utf-8") as file_iter:
        reader = csv.reader(file_iter)
        for idx, row in enumerate(reader):
            if idx >= skip_rows:
                yield row


def match_ext(path: Union[Path, str], expected_ext: str) -> bool:
    ext = filetype.guess_extension(path)
    if not ext or ext != expected_ext.lstrip(".").lower():
        logging.warning(f"Extension mismatch({expected_ext}) for {path}")
        return False
    return True


def excel_row_iter(path, sheet_index=0, skip_rows=0, values_only=False) -> Iterator[Tuple[Any]]:
    """Iterate rows in excel file(both xls and xlsx)"""
    date_mode = None
    if match_ext(path, "xlsx"):
        import openpyxl

        work_book = openpyxl.load_workbook(path)
        work_sheet = work_book.worksheets[sheet_index]
        rows = work_sheet.iter_rows(min_row=skip_rows + 1, values_only=values_only)
    else:
        import xlrd

        work_book = xlrd.open_workbook(path)
        work_sheet = work_book.sheet_by_index(sheet_index)
        date_mode = work_book.datemode
        rows = work_sheet.get_rows()
        for _ in range(skip_rows):
            next(rows)

    for row in rows:
        if date_mode is not None:
            # legacy code, for xlrd only
            for cell in row:
                cell.date_mode = date_mode
            if values_only:
                row = tuple(cell.value for cell in row)
        yield row


@unique
class Language(Enum):
    """
    语言类型
    """

    ZH_CN = "zh_CN"
    EN_US = "en_US"


@functools.lru_cache(1000)
def clean_txt(text, language=Language.ZH_CN.value, remove_cn_text=False, lstrip=False):
    if language == Language.ZH_CN.value:
        text = re.sub(r"\s+", "", text)
    elif language == Language.EN_US.value:
        # 英文内容换行需要用空格链接,且不能去空格
        text = re.sub(r"[\r\t\f\v\n]+", " ", text)
        text = re.sub(r"\s{2,}", " ", text)
        if lstrip:
            text = text.lstrip()
        else:
            text = text.strip()

    if remove_cn_text:
        text = re.sub(r"[\u4e00-\u9fa5]", "", text)
    return text


def time_wrapper(func):
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"Function {func.__name__} took {end - start} seconds")
        return result

    return wrapper
