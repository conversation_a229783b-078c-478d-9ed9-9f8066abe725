# 使用Python生成素描图

# pip install opencv-python

import cv2

image = cv2.imread("demo.png")
gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

inverted = 255 - gray_image
blur = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(inverted, (21, 21), 0)
inverted_blur = 255 - blur

sketch = cv2.divide(gray_image, inverted_blur, scale=256.0)

cv2.imwrite("after_sketched.png", sketch)
cv2.imshow("Image", sketch)
