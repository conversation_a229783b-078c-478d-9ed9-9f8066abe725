import gizeh

# 创建一个新的Gizeh画布
surface = gizeh.Surface(width=320, height=260)

# 绘制一个红色圆形
circle = gizeh.circle(r=40, xy=[156, 200], fill=(1, 0, 0))  # 红色圆形
circle.draw(surface)

# 绘制一个蓝色方块
square = gizeh.rectangle(lx=60, ly=60, xy=[80, 130], fill=(0, 0, 1))  # 蓝色方块
square.draw(surface)

# 手动绘制一个绿色三角形
triangle_points = [(210, 110), (270, 110), (240, 50)]  # 为三角形计算的点
triangle_shape = gizeh.polyline(triangle_points, close_path=True, fill=(0, 1, 0))  # 绿色三角形
triangle_shape.draw(surface)

# 导出为PNG图片
surface.write_to_png("drawing.png")
