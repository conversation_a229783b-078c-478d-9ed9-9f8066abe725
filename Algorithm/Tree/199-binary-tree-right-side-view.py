from typing import Optional, List

from Algorithm.Tree import TreeNode


class Solution:

    depth = 0

    def rightSideView(self, root: Optional[TreeNode]) -> List[int]:
        def traverse(node):
            if not node:
                return None
            self.depth += 1
            if len(res) < self.depth:
                res.append(node.val)

            traverse(node.right)
            traverse(node.left)
            self.depth -= 1

        res = []
        traverse(root)
        return res


if __name__ == "__main__":
    s = Solution()
    # left = TreeNode(2, None, TreeNode(5))
    # right = TreeNode(3, None, TreeNode(4))
    #
    # node = TreeNode(1, left, right)
    # res = s.rightSideView(node)
    # print(res)

    sub_left = TreeNode(4, None, TreeNode(7, None, TreeNode(9, None, TreeNode(10))))
    left = TreeNode(2, sub_left, TreeNode(5))
    right = TreeNode(3, None, TreeNode(6, TreeNode(8), None))

    node = TreeNode(1, left, right)
    res = s.rightSideView(node)
    print(res)
