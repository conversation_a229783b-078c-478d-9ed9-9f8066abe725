# 最长不含重复字符的子字符串
# 滑动窗口
class Solution:
    def lengthOfLongestSubstring(self, s: str) -> int:
        if len(s) < 2:
            return len(s)
        head = 0
        tail = 0

        res = 1
        while tail < len(s) - 1:
            tail += 1
            if s[tail] not in s[head:tail]:
                res = max(res, tail - head + 1)
            else:
                while s[tail] in s[head:tail]:
                    head += 1

        return res


# 动态规划


class SolutionDp:
    def lengthOfLongestSubstring(self, s: str) -> int:
        if not s:
            return 0
        dic = dict()
        dp = [0] * len(s)
        dp[0] = 1
        dic[s[0]] = 0
        for i in range(1, len(s)):
            print(dic)
            if s[i] not in dic:
                dp[i] = dp[i - 1] + 1
                dic[s[i]] = i
            else:
                if i - dic[s[i]] > dp[i - 1]:
                    dp[i] = dp[i - 1] + 1
                else:
                    dp[i] = i - dic[s[i]]
                dic[s[i]] = i

        return max(dp)


if __name__ == "__main__":
    s = SolutionDp()
    print(s.lengthOfLongestSubstring("abcabcccd"))
    print(s.lengthOfLongestSubstring("bbbbb"))
    print(s.lengthOfLongestSubstring("pwwkew"))
