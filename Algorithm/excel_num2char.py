class Solution:
    def convertToTitle(self, columnNumber: int) -> str:
        ans = list()
        while columnNumber > 0:
            columnNumber -= 1
            ss = chr(columnNumber % 26 + ord("A"))
            print(columnNumber % 26)
            print(ord("A"))
            print(ss)
            ans.append(ss)
            columnNumber //= 26
        return "".join(ans[::-1])


if __name__ == "__main__":
    a = 1
    s = Solution()
    print(s.convertToTitle(a))
