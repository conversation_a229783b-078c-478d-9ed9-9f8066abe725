class ListNode(object):
    def __init__(self, val, next=None):
        self.val = val
        self.next = next

    @classmethod
    def gen_from_list(cls, nums):
        dummy = ListNode(-1)
        tmp = dummy
        for num in nums:
            cur = cls(num)
            tmp.next = cur
            tmp = cur

        return dummy.next

    def __repr__(self):
        ret = f"Node({self.val})"
        cur = self
        while cur.next is not None:
            cur = cur.next
            ret += f"-->Node({cur.val})"
        return ret


def nextLargerNodes(head):
    if not head or not head.next:
        return [0]
    slow = head
    fast = head.next
    ret = []
    tmp = None
    tmp_is_change = False
    while slow:
        if not slow.next:
            ret.append(0)
            break

        tmp = tmp if tmp_is_change else slow.val
        if not fast.next and tmp != slow.val:
            ret.append(max(tmp, fast.val))
            if slow.next:
                slow = slow.next
                fast = slow.next
                tmp_is_change = False
            continue
        if fast and fast.val > slow.val and fast.val > tmp:
            tmp = fast.val
            tmp_is_change = True
            if fast.next:
                fast = fast.next
            else:
                ret.append(max(tmp, fast.val))
                if slow.next:
                    slow = slow.next
                    fast = slow.next
                    tmp_is_change = False
        else:
            if fast.next:
                fast = fast.next
            else:
                ret.append(0)
                if slow.next:
                    slow = slow.next
                    fast = slow.next
                    tmp_is_change = False

        return ret


def test():
    nodelist = [1, 7, 5, 1, 9, 2, 5, 1]
    s = ListNode.gen_from_list(nodelist)
    ss = nextLargerNodes(s)
    print(ss)


if __name__ == "__main__":
    test()
