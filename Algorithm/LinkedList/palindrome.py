from Algorithm.LinkedList.ListNode import ListNode


class Solution:
    # labuladong 插件里的写法
    def isPalindrome(self, head: ListNode) -> bool:
        self.left = head

        def recursively_check(right):
            if not right:
                return True
            res = recursively_check(right.next)
            res = res & (self.left.val == right.val)
            self.left = self.left.next
            return res

        return recursively_check(head)


class Solution1:
    # 对于官方教程写法的改动
    def isPalindrome(self, head: ListNode) -> bool:
        self.front_pointer = head

        def recursively_check(current_node=head):
            if not current_node:
                return True
            if not recursively_check(current_node.next):
                return False
            if self.front_pointer.val != current_node.val:
                return False
            self.front_pointer = self.front_pointer.next
            return True

        return recursively_check()

    # 官方教程的写法
    def isPalindrome1(self, head: ListNode) -> bool:
        self.front_pointer = head

        def recursively_check(current_node=head):
            if current_node is not None:
                if not recursively_check(current_node.next):
                    return False
                if self.front_pointer.val != current_node.val:
                    return False
                self.front_pointer = self.front_pointer.next
            return True

        return recursively_check()

    # 利用列表比较
    def use_list(self, head: ListNode) -> bool:
        res = []
        while head:
            res.append(head.val)
            head = head.next

        return res == res[::-1]

    # 利用快慢指针先找到中点，然后反转后半部分链表，再依次比较
    def use_fast_slow_pointer(self, head: ListNode) -> bool:
        # 找中点
        middle_node = self.find_middle_node(head)
        # 切分链表
        second_half = middle_node.next
        # 反转后半部分
        reversed_second_half = self.reverse_recursive(second_half)
        # 比较
        result = True
        left = head
        right = reversed_second_half
        while left and right:
            if left.val != right.val:
                result = False
            left = left.next
            right = right.next

        # 还原链表
        middle_node.next = self.reverse_iter(reversed_second_half)

        return result

    def find_middle_node(self, head: ListNode) -> ListNode:
        fast, slow = head, head
        while fast.next and fast.next.next:
            fast = fast.next.next
            slow = slow.next

        return slow

    def reverse_recursive(self, head):
        def reverse(head):
            if not head or not head.next:
                return head

            node = reverse(head.next)
            head.next.next = head
            head.next = None
            return node

        return reverse(head)

    def reverse_iter(self, head):
        prev = None
        cur = head
        while cur:
            next_node = cur.next
            cur.next = prev
            prev = cur
            cur = next_node

        return prev


def test_case(nodelist):
    nodelist = ListNode.gen_from_list(nodelist)
    solution = Solution()
    print(solution.isPalindrome(nodelist))
    solution1 = Solution1()
    print(solution1.isPalindrome(nodelist))
    print(solution1.isPalindrome1(nodelist))
    print(solution1.use_list(nodelist))
    print(solution1.use_fast_slow_pointer(nodelist))


if __name__ == "__main__":
    # 奇数节点测试用例
    nodelist = [1, 7, 2, 3, 2, 7, 1]
    test_case(nodelist)
    # 偶数节点
    nodelist = [1, 7, 2, 3, 2, 7, 1]
    test_case(nodelist)

    # bad case
    nodelist = [1, 7, 2, 3, 2, 2, 7, 1]
    test_case(nodelist)

    # bad case
    nodelist = [1, 7, 2, 2, 3, 7, 1]
    test_case(nodelist)

    # bad case
    nodelist = [1, 1, 2, 1]
    test_case(nodelist)
