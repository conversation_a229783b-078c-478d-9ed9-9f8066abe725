from LinkedList.ListNode import ListNode


def reverse_list(head: ListNode) -> ListNode:
    """
    反转链表
    """
    if head is None or head.next is None:
        return head
    current = reverse_list(head.next)
    head.next.next = head
    head.next = None
    return current


def merge_two_lists(l1: ListNode, l2: ListNode) -> ListNode:
    """
    合并两个有序链表
    """
    if l1 is None:
        return l2
    if l2 is None:
        return l1
    dummy = ListNode(-1)
    pre = dummy
    while l1 and l2:
        if l1.val < l2.val:
            pre.next = l1
            l1 = l1.next
        else:
            pre.next = l2
            l2 = l2.next
        pre = pre.next

    if l1:
        pre.next = l1
    else:
        pre.next = l2
    return dummy.next


if __name__ == "__main__":
    test1 = ListNode.gen_from_list([1, 2, 3, 4, 5])
    test2 = ListNode.gen_from_list([1, 4, 5])
    # 反转链表
    # print(reverse_list(test))
    # 合并两个有序链表
    print(merge_two_lists(test1, test2))
