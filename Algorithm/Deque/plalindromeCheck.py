from Deque import Deque


def plalindrome_check(str):
    deque = Deque()
    for i in str:
        deque.addFront(i)

    flag = True
    while flag:
        if deque.size() > 1:
            front = deque.removeFront()
            rear = deque.removeRear()
            if front != rear:
                return False
        else:
            flag = False
    return True


if __name__ == "__main__":
    str = "miami"
    print(plalindrome_check(str))
    str = "imami"
    print(plalindrome_check(str))
    str = "abccba"
    print(plalindrome_check(str))
