from typing import List


class Solution:
    def maxSlidingWindow(self, nums: List[int], k: int) -> List[int]:
        left = 0
        right = k
        ret = []
        while right < len(nums) + 1:
            sub_nums = nums[left:right]
            print(sub_nums)
            ret.append(max(sub_nums))
            left += 1
            right += 1

        return ret


if __name__ == "__main__":
    s = Solution()
    nums = [1, 3, -1, -3, 5, 3, 6, 7]
    k = 3
    print(s.maxSlidingWindow(nums, k))
