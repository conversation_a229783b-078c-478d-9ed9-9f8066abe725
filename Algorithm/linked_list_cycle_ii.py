# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, x):
#         self.val = x
#         self.next = None
from LinkedList.ListNode import ListNode


class Solution:
    def detectCycle(self, head: ListNode) -> ListNode:
        fast = head
        slow = head

        while fast and fast.next:
            fast = fast.next.next
            slow = slow.next

            while fast == slow:
                a = head
                b = fast
                while a != b:
                    a = a.next
                    b = b.next
                return a
        return None
