from typing import List


class Solution:
    def lemonadeChange(self, bills: List[int]) -> bool:
        five_list = []
        ten_list = []

        for i in bills:
            if i == 5:
                five_list.append(i)
            if i == 10:
                if not five_list:
                    return False
                five_list.pop()
                ten_list.append(i)
            if i == 20:
                if not five_list:
                    return False
                if ten_list:
                    five_list.pop()
                    ten_list.pop()
                elif len(five_list) > 3:
                    five_list.pop()
                    five_list.pop()
                    five_list.pop()
                else:
                    return False

        return True
