# CSV 行扩展工具

## 功能说明

这个脚本用于处理 `data/2025_ar_need_review.csv` 文件，将 `rule_type=B` 的行扩展为多行，每行对应一个 rule。

## 处理逻辑

1. **rule_type=A 的行**：保持不变
   - 从 URL 的 `schemaKey` 参数中提取 rule 值（例如：A10）
   - 添加 `rule` 列，填入提取的值

2. **rule_type=B 的行**：扩展为 17 行
   - 每行对应一个 B 类型的 rule（B12, B14, B15, B17, B18, B19, B22, B23, B24, B25, B26, B27, B28, B29, B30, B31, B52）
   - 在 URL 中添加 `&schemaKey={rule}` 参数
   - 添加 `rule` 列，填入对应的 rule 值

## 使用方法

### 1. 测试模式（推荐先运行）

测试模式只处理前 2 条数据，方便验证结果：

```bash
python tools/expand_b_rules.py --test
```

输出示例：
```
============================================================
🧪 测试模式
============================================================
📝 测试模式：只处理前 2 条数据

============================================================
📊 处理统计
============================================================
输入文件: data/2025_ar_need_review.csv
输出文件: data/2025_ar_need_review_expanded.csv

原始数据:
  - A 类型行数: 1
  - B 类型行数: 1
  - 总行数: 2

处理后数据:
  - A 类型行数: 1 (保持不变)
  - B 类型扩展后行数: 17 (每行扩展为 17 行)
  - 总行数: 18

📋 示例数据（前5行）:
...
```

### 2. 正式处理

确认测试结果正确后，运行正式处理：

```bash
python tools/expand_b_rules.py
```

## 输入输出

- **输入文件**: `data/2025_ar_need_review.csv`
- **输出文件**: `data/2025_ar_need_review_expanded.csv`

## 配置说明

如需修改配置，编辑 `tools/expand_b_rules.py` 文件中的配置区域：

```python
# ==================== 配置区域 ====================
INPUT_FILE = "data/2025_ar_need_review.csv"
OUTPUT_FILE = "data/2025_ar_need_review_expanded.csv"

# B 类型需要扩展的 rules
B_RULES = [
    "B12", "B14", "B15", "B17", "B18", "B19",
    "B22", "B23", "B24", "B25", "B26", "B27",
    "B28", "B29", "B30", "B31", "B52"
]

# 测试数据（只包含前2条数据）
TEST_MODE_LIMIT = 2
# ==================== 配置区域结束 ====================
```

## 处理示例

### 原始数据：
```csv
file_id,rule_type,stock_code,report_year,label_url
172730,A,01314,2025,http://...&schemaKey=A10
172730,B,01314,2025,http://...&projectId=17
```

### 处理后：
```csv
file_id,rule_type,stock_code,report_year,label_url,rule
172730,A,01314,2025,http://...&schemaKey=A10,A10
172730,B,01314,2025,http://...&projectId=17&schemaKey=B12,B12
172730,B,01314,2025,http://...&projectId=17&schemaKey=B14,B14
172730,B,01314,2025,http://...&projectId=17&schemaKey=B15,B15
... (共17行，对应17个B类rule)
```

## 注意事项

1. ⚠️ **先在测试模式下运行**，确认输出正确后再运行正式处理
2. 输出文件会覆盖已存在的同名文件
3. 脚本会自动为每个 B 类型的行生成 17 行数据
4. A 类型的行会保持不变，只添加 rule 列
