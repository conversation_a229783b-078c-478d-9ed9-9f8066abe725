import redis


def main():
    # pfadd 10000 data to redis
    client = redis.StrictRedis()
    for i in range(100000):
        client.pfadd("codehole11", str(i))

    for i in range(100000, 200000):
        client.pfadd("codehole21", str(i))

    total = client.pfcount("codehole11")
    total2 = client.pfcount("codehole21")
    print(total)
    print(total2)
    print(total + total2)
    client.pfmerge("codehole31", "codehole11", "codehole21")
    print(client.pfcount("codehole31"))


if __name__ == "__main__":
    main()
