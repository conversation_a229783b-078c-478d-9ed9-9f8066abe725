def split_continued_boxes(
    line_boxes: list[tuple[int, int, int, int]], threshold: float = 20
) -> list[list[tuple[int, int, int, int]]]:
    """
    通过比较相邻行的位置差异来划分文本框组。

    Args:
        line_boxes: 文本框坐标列表，每个元素为 (x1, y1, x2, y2)
        threshold: 位置差异阈值，默认为20.0

    Returns:
        list[list[tuple]]: 分组后的文本框列表
    """
    if not line_boxes:
        return []
    if len(line_boxes) == 1:
        return [line_boxes]

    result = []
    current_group = [line_boxes[0]]

    # 遍历相邻的两行进行比较
    for i in range(len(line_boxes) - 1):
        current_box = line_boxes[i]
        next_box = line_boxes[i + 1]

        # 计算left和right的差异
        left_diff = abs(current_box[0] - next_box[0])
        right_diff = abs(current_box[2] - next_box[2])

        # 如果任一差异超过阈值，开始新的分组
        if left_diff > threshold or right_diff > threshold:
            result.append(current_group)
            current_group = [next_box]
        else:
            current_group.append(next_box)

    # 添加最后一组
    if current_group:
        result.append(current_group)

    return result


# 测试代码
def test_split_boxes():
    # 测试用例
    boxes = boxes = [
        (366.0302, 133.2997, 537.998, 141.8674),
        (366.0392, 145.2864, 537.9089, 153.8271),
        (365.9762, 157.2731, 534.9664, 165.8588),
        (366.1112, 169.2599, 537.7011, 177.7916),
        (366.462, 181.2466, 481.1984, 189.7783),
        (85.2786, 411.6641, 348.3622, 419.8902),
        (85.7014, 423.7408, 249.08, 431.877),
    ]

    result = split_continued_boxes(boxes)

    # 打印结果
    print(f"分成了 {len(result)} 组")
    for i, group in enumerate(result, 1):
        print(f"第 {i} 组:")
        for box in group:
            print(f"    {box}")


if __name__ == "__main__":
    test_split_boxes()
