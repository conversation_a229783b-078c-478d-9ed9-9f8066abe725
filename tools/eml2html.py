#
# # Load EML message
# eml = MailMessage.load("/Users/<USER>/Downloads/Fw__高频数据_.html")
#
# # Set SaveOptions
# options = SaveOptions.default_html
# options.embed_resources = False
# options.HtmlFormatOptions = (
#     HtmlFormatOptions.WriteHeader | HtmlFormatOptions.WriteCompleteEmailAddress
# )  # save the message headers to output HTML using the formatting options
#
# # Convert EML to HTML
# eml.save("/Users/<USER>/Downloads/SaveAsHTML.html", options)
