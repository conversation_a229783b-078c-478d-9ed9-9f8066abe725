import re

from spellchecker import <PERSON><PERSON>l<PERSON><PERSON><PERSON>


def is_correct_spelling_sentence(sentence):
    spell = SpellChecker()
    words = sentence.split()
    words = [re.sub(r"[^A-Za-z0-9]+", "", word.lower()) for word in words]
    words = [word for word in words if word]
    misspelled = spell.unknown(words)
    if not words or not misspelled or len(words) <= 3:
        return True
    return len(misspelled) / len(words) < 0.382
