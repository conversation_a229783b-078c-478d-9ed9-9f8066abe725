import requests
from bs4 import BeautifulSoup
import pandas as pd
import time

from urllib3.exceptions import InsecureRequestWarning

# Suppress only the InsecureRequestWarning from urllib3 needed for this example
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)


def scrape_month(month, board_type, verify_ssl=True, retries=3, delay=5):
    if board_type == 'main':
        url = f"https://webb-site.com/dbpub/yearendcos.asp?e=m&sort=nameup&m={month}"
    elif board_type == 'gem':
        url = f"https://webb-site.com/dbpub/yearendcos.asp?e=g&sort=nameup&m={month}"
    else:
        raise ValueError("Invalid board_type. Use 'main' or 'gem'.")

    for attempt in range(retries):
        try:
            response = requests.get(url, verify=verify_ssl)
            response.raise_for_status()  # Raise an exception for bad status codes
            break
        except requests.exceptions.RequestException as e:
            print(f"Attempt {attempt + 1} failed for {board_type} board, month {month}: {e}")
            if attempt == retries - 1:
                print(f"Failed to retrieve data for {board_type} board, month {month} after {retries} attempts.")
                return None
            time.sleep(delay)

    soup = BeautifulSoup(response.content, 'html.parser')

    table = soup.select_one('body > div.mainbody > table')
    if not table:
        return None

    data = []
    rows = table.find_all('tr')[1:]  # Skip the header row
    for row in rows:
        cols = row.find_all('td')
        if len(cols) == 3:
            stock_code = cols[1].text.strip()
            company = cols[2].text.strip()
            data.append(
                {
                    'month': month,
                    'stock_code': stock_code,
                    'company': company,
                    'type': 'main' if board_type == 'main' else 'gem',
                }
            )

    return data


# Scrape data for all 12 months for both Main Board and GEM
all_data = []
verify_ssl = True  # Set this to False if SSL verification fails

for board_type in ['main', 'gem']:
    for month in range(1, 13):
        month_data = scrape_month(month, board_type, verify_ssl=verify_ssl)
        if month_data:
            all_data.extend(month_data)
        time.sleep(2)  # Add a delay between requests to be polite to the server

# Create a pandas DataFrame
df = pd.DataFrame(all_data)

# Save to CSV
df.to_csv('webb_site_companies_main_and_gem.csv', index=False)
print(f"Data saved to webb_site_companies_main_and_gem.csv")

# Display the first few rows
print(df.head())

# Display some basic statistics
print("\nData Summary:")
print(df['type'].value_counts())
print("\nCompanies per month:")
print(df.groupby('month').size())
