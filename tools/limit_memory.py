import resource
from functools import wraps


def limit_memory(maxsize):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):

            soft, hard = resource.getrlimit(resource.RLIMIT_DATA)
            resource.setrlimit(resource.RLIMIT_DATA, (maxsize, hard))

            try:
                result = func(*args, **kwargs)
            finally:
                resource.setrlimit(resource.RLIMIT_DATA, (soft, hard))

            return result

        return wrapper

    return decorator


def main():
    @limit_memory(1024 * 1024)  # 1MB
    def my_func():
        ...

    # @func_set_timeout(60)
    @limit_memory(8 * 1024**3)  # ＃ 内存限制为8GB
    def transform_time(data, ops=None):
        """transform if ops is None:"""
        ...
