import re

from utils import DATA_PATH

pattern = re.compile(r"crude score")
# pattern = re.compile(r'add_missing_crude_answer')


def main():
    # s = '2023-02-27 18:13:23,106 - [DEBUG] [MainThread] (predictor:726)
    # add_missing_crude_answer: schema_name : A1 law compliance - emissions, crude score: 0.10503874377323985'
    # print(pattern.search(s))
    file_path = DATA_PATH / "predictor.log"
    with open(file_path, "r", encoding="utf-8") as file_obj:
        for line in file_obj.readlines():
            if pattern.search(line.strip()):
                print(line)


if __name__ == "__main__":
    main()
