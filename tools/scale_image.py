from PIL import Image


def compress_image(input_path, output_path, target_size):
    img = Image.open(input_path)
    img.save(output_path, quality=85, optimize=True)

    while img.tell() > target_size:
        img = img.resize((int(img.width * 0.9), int(img.height * 0.9)), Image.ANTIALIAS)
        img.save(output_path, quality=85, optimize=True)


input_path = "/Users/<USER>/Downloads/WechatIMG1306012121.jpg"  # 输入图片路径
output_path = "/Users/<USER>/Downloads/WechatIMG1306012121-small11.jpg"  # 输出图片路径
target_size = 800 * 512  # 目标大小（800KB）

compress_image(input_path, output_path, target_size)
print("Compression Complete")
