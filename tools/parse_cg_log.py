import csv
import re
from datetime import datetime

name_map = {
    '366': '<PERSON>',
    '301': 'pai_jinjin',
    '156': 'pai_zenghuiqin',
}


def parse_log_line(line):
    # Extract timestamp
    timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
    timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S') if timestamp_match else None

    # Extract URL
    url_match = re.search(r'GET (.*?) ', line)
    url = url_match.group(1) if url_match else None

    # Extract UID
    uid_match = re.search(r'UID:(\d+)', line)
    uid = uid_match.group(1) if uid_match else None

    resp_status_match = re.search(r'\(server:80\) (\d+) GET ', line)
    resp_status = resp_status_match.group(1) if resp_status_match else None

    return name_map.get(uid, ''), url, resp_status, timestamp


def process_log(log_content):
    parsed_data = []
    for line in log_content.split('\n'):
        if line.strip():
            uid, url, resp_status, timestamp = parse_log_line(line)
            if uid and url and timestamp:
                parsed_data.append((uid, url, resp_status, timestamp.strftime('%Y-%m-%d %H:%M:%S')))
    return parsed_data


def write_csv(data, filename='log_output.csv'):
    with open(filename, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['UID', 'URL', 'resp status', 'Time'])  # Write header
        writer.writerows(data)


with open('/Users/<USER>/Downloads/cg_export.log', 'r') as file_obj:
    log_content = file_obj.read()
    parsed_data = process_log(log_content)
    write_csv(parsed_data)

print("CSV file 'log_output.csv' has been created with the parsed log data.")
