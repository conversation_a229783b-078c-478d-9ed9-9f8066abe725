import pandas as pd
import argparse
import os


def process_group(group):
    # 计算Comply的数量
    comply_count = (group['predict_value'] == 'comply').sum()

    # 创建结果字典
    result = {'comply_count': comply_count}

    # 为每个规则添加结果
    for rule in rules:
        rule_value = group[group['rule'] == rule]['predict_value'].iloc[0]
        result[rule] = rule_value

    return pd.Series(result)


def main(input_file, output_file):
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 '{input_file}' 不存在")
        exit(1)

    # 读取CSV文件
    df = pd.read_csv(input_file)

    # 创建规则列表
    global rules
    rules = [
        'T1Reference to ISSB Standards',
        'T2Adoption of Independent Assurance',
        'T3Details of Independent Assurance',
        'T4Independent Assurance on scope 1 and scope 2 GHG emissions',
        'T5Scenario analysis',
        'T6Source of scenarios',
        'T7Scope 3 emissions',
        'T8Categories of scope 3 emissions',
        'T9Scope 3 emissions data by categories',
    ]

    # 转换数据
    result_df = df.groupby(['stock_code', 'report_year']).apply(process_group).reset_index()

    # 重命名列以使其更简洁
    column_mapping = {
        'T1Reference to ISSB Standards': 'T1 Reference to ISSB Standards',
        'T2Adoption of Independent Assurance': 'T2 doption of Independent Assurance',
        'T3Details of Independent Assurance': 'T3 Details of Independent Assurance',
        'T4Independent Assurance on scope 1 and scope 2 GHG emissions': 'T4 Independent Assurance on scope 1 and scope 2 GHG emissions',
        'T5Scenario analysis': 'T5 Scenario analysis',
        'T6Source of scenarios': 'T6 Source of scenarios',
        'T7Scope 3 emissions': 'T7 Scope 3 emissions',
        'T8Categories of scope 3 emissions': 'T8 Categories of scope 3 emissions',
        'T9Scope 3 emissions data by categories': 'T9 Scope 3 emissions data by categories',
    }
    result_df = result_df.rename(columns=column_mapping)

    # 调整列的顺序
    columns_order = ['stock_code', 'report_year', 'comply_count'] + list(column_mapping.values())
    result_df = result_df[columns_order]

    # 保存结果到新的CSV文件
    result_df.to_csv(output_file, index=False)

    print(f"处理完成，结果已保存到: {output_file}")
    print("\n数据预览:")
    print(result_df.head())


if __name__ == '__main__':
    input_file = '/Users/<USER>/workspace/xx/tools/hkex/transform_esg/policy-esg-enum-20250529.csv'
    output_file = '/Users/<USER>/workspace/xx/tools/hkex/transform_esg/policy-esg-enum-20250529-summary.csv'
    main(input_file, output_file)
