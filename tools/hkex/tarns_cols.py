import pandas as pd
from googletrans import Translator

# 读取Excel文件
df = pd.read_excel(
    '/Users/<USER>/workspace/xx/tools/hkex/Jura 6_IR_Log_Reports_AGM & Poll_20250221.xlsx', sheet_name='AGM UAT issues'
)
print("列名:", list(df.columns))

# 初始化翻译器
translator = Translator()

# 翻译函数
def translate_text(text):
    try:
        if pd.isna(text):
            return ''
        result = translator.translate(str(text), dest='zh-cn')
        return result.text
    except Exception as e:
        print(f"翻译出错: {e}")
        return text


# 获取D列的数据并添加翻译列
column_name = df.columns[3]  # 获取D列的列名
df[f'{column_name}_CN'] = df[column_name].apply(translate_text)

# 保存结果
df.to_excel(
    '/Users/<USER>/workspace/xx/tools/hkex/Jura 6_IR_Log_Reports_AGM & Poll_20250221_translated.xlsx', index=False
)
