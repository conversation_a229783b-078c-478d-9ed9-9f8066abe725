import multiprocessing as mp
import threading
import time


# ---------------------------------------------------------
# 抛出异常，子进程中没有name资源；NameError: name 'name' is not defined

# def f():
#     print(name)
#
#
# if __name__ == "__main__":
#     mp.set_start_method("spawn")
#     name = "123"
#     p = mp.Process(target=f)  # name 变量并未拷贝到子进程中，而需要通过参数的形式传递给子进程
#     p.start()


# # 将所需资源传递给子进程

# def f(name):
#     print(name)
#     print(f"id of name: {id(name)}")
#
#
# if __name__ == "__main__":
#     mp.set_start_method("spawn")
#
#     name = "123"
#     p = mp.Process(target=f, args=(name,))
#     p.start()
#     print(f"parent process's id of name: {id(name)}")  # id of name 的值不同，说明子进程会将传递进来的资源深拷贝一份。


# def f(x):
#     print(x)
#
#
# if __name__ == "__main__":
#     mp.set_start_method("spawn")
#
#     fb = open("test.txt", "wt")
#     lock = threading.Lock()
#     p1 = mp.Process(target=f, args=(fb,))
#     p1.start()  # TypeError: cannot serialize '_io.TextIOWrapper' object
#
#     p2 = mp.Process(target=f, args=(lock,))
#     p2.start()  # TypeError: can't pickle _thread.lock objects


def f():
    time.sleep(30)


if __name__ == "__main__":
    mp.set_start_method("fork")

    p2 = mp.Process(target=f)
    p2.start()
    time.sleep(60)  # 通过ps -ef|grep python，发现前30秒有两个python解释器，后30秒只有一个
