import time

import numpy as np


def kmeans(nums, clusters):
    """k-means聚类算法

    k       - 指定分簇数量
    ds      - ndarray(m, n)，m个样本的数据集，每个样本n个属性值
    """

    sample_size, attrs = nums.shape  # m：样本数量，n：每个样本的属性值个数
    result = np.empty(sample_size, dtype=np.int)  # m个样本的聚类结果
    cores = nums[
        np.random.choice(np.arange(sample_size), clusters, replace=False)
    ]  # 从m个数据样本中不重复地随机选择k个样本作为质心

    while True:  # 迭代计算
        square = np.square(
            np.repeat(nums, clusters, axis=0).reshape(sample_size, clusters, attrs)
            - cores
        )
        distance = np.sqrt(np.sum(square, axis=2))  # ndarray(m, k)，每个样本距离k个质心的距离，共有m行
        index_min = np.argmin(distance, axis=1)  # 每个样本距离最近的质心索引序号

        if (index_min == result).all():  # 如果样本聚类没有改变
            return result, cores  # 则返回聚类结果和质心数据

        result[:] = index_min  # 重新分类
        for i in range(clusters):  # 遍历质心集
            items = nums[result == i]  # 找出对应当前质心的子样本集
            cores[i] = np.mean(items, axis=0)  # 以子样本集的均值作为当前质心的位置


if __name__ == "__main__":
    k = 2
    ds = [
        0.9303215601842983,
        0.9149399519254957,
        0.8742789813906421,
        0.6031185157463119,
        0.15511126025727384,
        0.07593772475647748,
        0.01625873339842997,
        0.015552018549929403,
        0.012898249753767355,
        0.008737941904016009,
    ]
    ds = [[i] for i in ds]
    ds = np.asarray(ds)
    t0 = time.time()
    current_result, current_cores = kmeans(ds, k)
    print(current_result)
    t = time.time() - t0
