from PIL import Image, ImageDraw, ImageFont
import os


def add_watermark_to_idcard(image_path, output_path=None, watermark_text="仅供办理证书，其他无效"):
    """
    给身份证图片添加水印

    Args:
        image_path: 身份证图片路径
        output_path: 输出图片路径，如果为None则在原文件名后添加_watermarked
        watermark_text: 水印文本内容

    Returns:
        输出图片的路径
    """
    # 打开图片
    try:
        img = Image.open(image_path)
        # 转换为RGBA模式以支持透明度
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
    except Exception as e:
        print(f"无法打开图片: {e}")
        return None

    # 创建一个透明的图层用于绘制水印
    watermark = Image.new('RGBA', img.size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(watermark)

    # 尝试加载字体，如果失败则使用默认字体
    try:
        # 根据图片大小选择合适的字体大小
        font_size = int(min(img.size) / 15)
        font = ImageFont.truetype("Arial.ttf", font_size)
    except:
        # 如果找不到指定字体，使用默认字体
        font = ImageFont.load_default()

    # 计算文本大小以便定位
    text_width, text_height = draw.textsize(watermark_text, font=font)

    # 在图片上多个位置添加半透明水印
    for i in range(0, img.width, text_width + 50):
        for j in range(0, img.height, text_height + 50):
            # 设置水印文字为红色，半透明
            draw.text((i, j), watermark_text, font=font, fill=(255, 0, 0, 100))

    # 将水印图层与原图合并
    watermarked_img = Image.alpha_composite(img, watermark)

    # 如果没有指定输出路径，则在原文件名后添加_watermarked
    if output_path is None:
        filename, ext = os.path.splitext(image_path)
        output_path = f"{filename}_watermarked{ext}"

    # 保存图片
    watermarked_img = watermarked_img.convert('RGB')  # 转换回RGB模式以支持所有格式
    watermarked_img.save(output_path)

    print(f"水印已添加，图片保存至: {output_path}")
    return output_path


def main():
    # 示例用法
    image_path = input("请输入身份证图片路径: ")
    output_path = input("请输入输出图片路径(留空则自动生成): ") or None
    watermark_text = input("请输入水印文字(留空则使用默认文字): ") or "仅供办理证书，其他无效"

    add_watermark_to_idcard(image_path, output_path, watermark_text)


if __name__ == "__main__":
    main()
