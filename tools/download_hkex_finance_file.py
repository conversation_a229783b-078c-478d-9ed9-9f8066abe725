import datetime
import logging
import time
from pprint import pprint

import requests
from bs4 import BeautifulSoup
import os

from utils import clean_txt, Language

logger = logging.getLogger(__name__)

# 定义一个伪装的User-Agent列表，可以选择性地轮换使用
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36",
    # 可以根据需要添加更多...
]

urls = [
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2014?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2015?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2016?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2017?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2018?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2019?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2020?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2021?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2022?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2023?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2024?sc_lang=en",
    "https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/2025?sc_lang=en",
]
normal_years = [
    # '2014',
    # '2015',
    # '2016',
    # '2017',
]

has_none_years = [
    # '2018',
]

special_url_years = [
    # '2019',
]
intext_url_years = [
    # '2020',
    # '2021',
    # '2022',
    '2023',
    # '2024',
    # '2025',
]
report_years = [
    *normal_years,
    *has_none_years,
    *special_url_years,
    *intext_url_years,
    # '2022',
    # '2023',
    # '2024',
    # '2025',
]

# 定义保存文件的目录
save_dir = "pdf_files"


def get_doc_type(pdf_url, full_url, link_title):
    pdf_url = pdf_url.lower()
    full_url = full_url.lower()
    link_title = link_title.lower()
    if 'final' in pdf_url:
        return 'Annual_Results'
    if '3q' in pdf_url:
        return 'Third_Quarter_Results'
    if 'interim' in pdf_url:
        return 'Interim_Results'
    if '1q' in pdf_url:
        return 'First_Quarter_Results'
    if 'q3-results' in full_url:
        return 'Third_Quarter_Results'

    if 'q1 results announcement' in link_title:
        return 'First_Quarter_Results'
    if 'interim financial statements' in link_title:
        return 'First_Quarter_Results'
    if 'q3 results announcement' in link_title:
        return 'Third_Quarter_Results'


def get_release_date(pdf_url):
    date_time_str = pdf_url.split('/')[-1].split('_')[0]
    date_time_str = f'20{date_time_str}'
    # 20160302 格式转换为 2016-03-02
    try:
        res = datetime.datetime.strptime(date_time_str, '%Y%m%d').strftime('%Y-%m-%d')
    except:
        res = date_time_str
    return res


def main():
    headers = {'User-Agent': USER_AGENTS[0]}  # 使用列表中的第一个User-Agent
    for report_year in report_years:
        print(f'{report_year=}')
        url = (
            f'https://www.hkexgroup.com/Investor-Relations/Financial-Results-and-Presentations/{report_year}?sc_lang=en'
        )
        response = requests.get(url, headers=headers, verify=False)
        if response.status_code == 200:
            # 使用BeautifulSoup解析网页内容
            soup = BeautifulSoup(response.text, 'html.parser')

            # 找到所有title属性为"Results Announcement"的<a>标签
            links = soup.find_all('a', title="Results Announcement")
            id_links = soup.find_all('a', id="Results Announcement")
            string_links = soup.find_all('a', string="Results Announcement")

            links += id_links
            links += string_links
            if report_year in intext_url_years:
                intext_links = soup.find_all('a', class_='intext_link')
                links += intext_links
            links = set(links)

            # 如果目录不存在，则创建
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            print(f'{len(links)=}')
            # 遍历所有找到的链接
            for link in links:
                # 获取PDF文件的URL
                pdf_url = link.get('href')
                if not pdf_url:
                    continue
                link_title = clean_txt(link.contents[0], language=Language.EN_US) if link.contents else ''
                if link_title and 'Webcast' in link_title:
                    continue
                if any(x in pdf_url for x in ['vF', 'Regulatory-Reports', 'Investor-Relations']):
                    continue
                # 构造完整的URL
                if not pdf_url.startswith('https'):
                    full_url = f"https://www.hkexgroup.com{pdf_url}"
                else:
                    full_url = pdf_url

                print(f'{full_url=}')
                # 发送GET请求下载PDF文件
                response = requests.get(full_url, stream=True, verify=False, headers=headers)

                # 检查请求是否成功
                if response.status_code == 200:

                    pdf_url = response.url
                    print(f'{pdf_url=}')
                    doc_type = get_doc_type(pdf_url, full_url, link_title)
                    if not doc_type:
                        print('not found doc_type, skip')
                        continue
                    release_date = get_release_date(pdf_url)
                    file_name = f'{report_year}_{doc_type}_{release_date}.pdf'

                    # 构造完整的保存路径
                    save_path = os.path.join(save_dir, file_name)

                    # 写入文件
                    with open(save_path, "wb") as file:
                        for chunk in response.iter_content(chunk_size=1024):
                            file.write(chunk)

                    print(f'vaild_pdf_url: {pdf_url}')
                    print(f"{file_name} has been downloaded successfully.")
                else:
                    print(f"Failed to download {full_url}, status code: {response.status_code}")
        else:
            print(f"Failed to retrieve the webpage, status code: {response.status_code}")
        time.sleep(10)


if __name__ == '__main__':
    main()
