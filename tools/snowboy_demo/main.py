import os
import signal
import snowboydetect as snowboydecoder


class Rundev:
    def __init__(self, model, sensitivity=0.5, sleep_time=0.03):
        # 外置参数
        self.model = model
        self.sensitivity = sensitivity
        self.sleep_time = sleep_time

        # 内置参数
        self.interrupted = False

    def interrupt_callback(self):
        return self.interrupted

    def signal_handler(self, signal, frame):
        self.interrupted = True

    def run(self):
        print("正在监听中.........", "按 Ctrl+C 停止运行")

        # capture SIGINT signal, e.g., Ctrl+C
        signal.signal(signal.SIGINT, self.signal_handler)

        detector = snowboydecoder.HotwordDetector(self.model, sensitivity=self.sensitivity)

        # main loop
        detector.start(
            detected_callback=snowboydecoder.play_audio_file,
            interrupt_check=self.interrupt_callback,
            sleep_time=self.sleep_time,
        )
        # 使终止
        detector.terminate()

        # 测试


if __name__ == "__main__":
    # os.getcwd()获取当前工作路径
    # args = [
    #     'python3',
    #     os.getcwd() + "/python/snowBoyDemo/demo.py",
    #     os.getcwd() + "/python/snowBoyDemo/xiaoai.pmdl"
    # ]
    # dev = Run(args=args)
    # dev.run()
    dev = Rundev(os.getcwd() + "test.pmdl")
    dev.run()
