#!/usr/bin/env python3
"""
将 CSV 文件中 rule_type=B 的行扩展为多行，每行对应一个 rule

使用方法:
    python expand_b_rules.py              # 处理真实数据
    python expand_b_rules.py --test       # 使用测试数据
"""

import csv
import argparse
from pathlib import Path


# ==================== 配置区域 ====================
INPUT_FILE = "data/2025_ar_need_review.csv"
OUTPUT_FILE = "data/2025_ar_need_review_expanded.csv"

# B 类型需要扩展的 rules
B_RULES = [
    "B12", "B14", "B15", "B17", "B18", "B19",
    "B22", "B23", "B24", "B25", "B26", "B27",
    "B28", "B29", "B30", "B31", "B52"
]

# 测试数据（只包含前2条数据）
TEST_MODE_LIMIT = 2
# ==================== 配置区域结束 ====================


def extract_rule_from_url(url):
    """
    从 URL 中提取 schemaKey 参数的值
    例如: http://...&schemaKey=A10 -> A10
    """
    if "schemaKey=" in url:
        # 找到 schemaKey= 后面的值
        start = url.find("schemaKey=") + len("schemaKey=")
        # 找到下一个 & 或结尾
        end = url.find("&", start)
        if end == -1:
            return url[start:]
        else:
            return url[start:end]
    return ""


def add_schema_key_to_url(url, rule):
    """
    在 URL 中添加 &schemaKey={rule} 参数
    """
    if "?" in url:
        # URL 已经有参数，直接添加
        return f"{url}&schemaKey={rule}"
    else:
        # URL 没有参数，添加第一个参数
        return f"{url}?schemaKey={rule}"


def process_csv(input_file, output_file, test_mode=False):
    """
    处理 CSV 文件

    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        test_mode: 是否为测试模式（只处理前几条数据）
    """
    # 确保输入文件存在
    if not Path(input_file).exists():
        raise FileNotFoundError(f"输入文件不存在: {input_file}")

    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        fieldnames = reader.fieldnames
        rows = list(reader)

    # 如果是测试模式，只处理前几条
    if test_mode:
        rows = rows[:TEST_MODE_LIMIT]
        print(f"📝 测试模式：只处理前 {TEST_MODE_LIMIT} 条数据")

    # 添加新列 'rule' 到字段列表
    new_fieldnames = list(fieldnames) + ['rule']

    # 处理数据
    expanded_rows = []
    a_count = 0
    b_count = 0
    b_expanded_count = 0

    for row in rows:
        rule_type = row['rule_type']

        if rule_type == 'A':
            # A 类型保持不变，从 URL 中提取 rule
            rule = extract_rule_from_url(row['label_url'])
            new_row = row.copy()
            new_row['rule'] = rule
            expanded_rows.append(new_row)
            a_count += 1

        elif rule_type == 'B':
            # B 类型扩展为 17 行
            b_count += 1
            for rule in B_RULES:
                new_row = row.copy()
                # 在 URL 中添加 schemaKey 参数
                new_row['label_url'] = add_schema_key_to_url(row['label_url'], rule)
                new_row['rule'] = rule
                expanded_rows.append(new_row)
                b_expanded_count += 1

    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=new_fieldnames)
        writer.writeheader()
        writer.writerows(expanded_rows)

    # 显示统计信息
    print("\n" + "=" * 60)
    print("📊 处理统计")
    print("=" * 60)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"\n原始数据:")
    print(f"  - A 类型行数: {a_count}")
    print(f"  - B 类型行数: {b_count}")
    print(f"  - 总行数: {a_count + b_count}")
    print(f"\n处理后数据:")
    print(f"  - A 类型行数: {a_count} (保持不变)")
    print(f"  - B 类型扩展后行数: {b_expanded_count} (每行扩展为 {len(B_RULES)} 行)")
    print(f"  - 总行数: {len(expanded_rows)}")
    print("=" * 60)

    # 如果是测试模式，显示示例数据
    if test_mode:
        print("\n📋 示例数据（前5行）:")
        print("=" * 60)
        for i, row in enumerate(expanded_rows[:5], 1):
            print(f"\n第 {i} 行:")
            print(f"  file_id: {row['file_id']}")
            print(f"  rule_type: {row['rule_type']}")
            print(f"  stock_code: {row['stock_code']}")
            print(f"  rule: {row['rule']}")
            print(f"  label_url: {row['label_url'][:80]}...")


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="扩展 CSV 文件中的 B 类型行")
    parser.add_argument(
        "--test",
        action="store_true",
        help=f"测试模式（只处理前 {TEST_MODE_LIMIT} 条数据）"
    )
    args = parser.parse_args()

    try:
        # 显示模式
        if args.test:
            print("=" * 60)
            print("🧪 测试模式")
            print("=" * 60)
        else:
            print("=" * 60)
            print("🚀 正式处理模式")
            print("=" * 60)

        # 处理文件
        process_csv(INPUT_FILE, OUTPUT_FILE, test_mode=args.test)

        print("\n✅ 处理完成！")

    except Exception as e:
        print(f"\n❌ 错误: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
