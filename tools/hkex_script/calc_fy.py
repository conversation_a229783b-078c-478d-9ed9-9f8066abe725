import calendar
from datetime import datetime, timedelta

from dateutil.relativedelta import relativedelta
import csv


def add_months_get_end_of_month(date_str, months_to_add):
    # Parse the input date string
    date = datetime.strptime(date_str, "%Y/%m/%d")

    # Add months using relativedelta
    new_date = date + relativedelta(months=months_to_add)

    # Get the last day of the resulting month
    _, last_day = calendar.monthrange(new_date.year, new_date.month)

    # Create the result date with the last day of the month
    result = new_date.replace(day=last_day)

    return result.strftime("%Y/%m/%d")


def clac_q1_q3(fy_end, months_to_add):
    date = add_months_get_end_of_month(fy_end, months_to_add)
    date = datetime.strptime(date, "%Y/%m/%d")
    new_date = date + timedelta(days=45)
    return new_date.strftime("%Y/%m/%d")


def clac_interim(fy_end):
    return (datetime.datetime.strptime(fy_end, "%Y/%m/%d") - relativedelta(months=4)).strftime("%Y-%m-%d")


csv_data = []
date_strings = [
    "2024/02/28",
    "2024/03/31",
    "2024/04/30",
    "2024/05/31",
    "2024/06/30",
    "2024/07/31",
    "2024/08/31",
    "2024/09/30",
    "2024/10/31",
    "2024/11/30",
    "2024/12/31",
]
csv_data.append(['FY end'] + date_strings)

data = ['Annual report']
for date in date_strings:
    data.append(add_months_get_end_of_month(date, 4))
csv_data.append(data)

data = ['Results announcement (Q1) - GEM']
for date in date_strings:
    data.append(clac_q1_q3(date, -9))
csv_data.append(data)


data = ['Results announcement (Interim)']
for date in date_strings:
    data.append(add_months_get_end_of_month(date, -4))
csv_data.append(data)

data = ['Results announcement (Q3) - GEM']
for date in date_strings:
    data.append(clac_q1_q3(date, -3))
csv_data.append(data)

data = ['Results announcement (Final)']
for date in date_strings:
    data.append(add_months_get_end_of_month(date, 3))
csv_data.append(data)

data = ['ESG report']
for date in date_strings:
    data.append(add_months_get_end_of_month(date, 4))
csv_data.append(data)

data = ['CG report']
for date in date_strings:
    data.append(add_months_get_end_of_month(date, 4))
csv_data.append(data)


# 将数据写入CSV文件
with open('/Users/<USER>/Downloads/financial_reporting_dates.csv', 'w', newline='') as file:
    writer = csv.writer(file)
    writer.writerows(csv_data)

print("CSV file 'financial_reporting_dates.csv' has been generated.")
