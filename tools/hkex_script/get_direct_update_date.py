from playwright.sync_api import sync_playwright
import re
from datetime import datetime


def get_last_updated_date():
    with sync_playwright() as p:
        try:
            # 启动浏览器
            browser = p.chromium.launch(headless=True)
            context = browser.new_context()
            page = context.new_page()

            # 1. 访问初始页面并等待加载完成
            page.goto('https://www3.hkexnews.hk/reports/dirsearch?sc_lang=en')
            # 等待页面加载完成
            page.wait_for_load_state('networkidle')

            # 2. 等待输入框出现并输入"1"
            input_field = page.locator('#searchby_stockcode_txt')
            input_field.wait_for(state='visible')
            input_field.fill('1')

            # 3. 点击搜索按钮
            # 使用更精确的选择器并增加等待时间
            search_button = page.locator('#searchbutton')  # 使用ID选择器
            search_button.wait_for(state='visible', timeout=60000)  # 增加等待时间到60秒
            # 确保按钮可点击
            search_button.evaluate('button => button.click()')

            # 4. 等待结果加载
            page.wait_for_load_state('networkidle')
            # 等待更新日期文本出现
            page.wait_for_selector('text=Updated :', timeout=60000)
            updated_element = page.locator('text=Updated :').first
            updated_text = updated_element.text_content()

            # 提取日期
            date_match = re.search(r'Updated\s*:\s*(\d+\s+\w+\s+\d{4})', updated_text)
            if date_match:
                return date_match.group(1)
            else:
                return "Date not found"

        except Exception as e:
            return f"Error during scraping: {str(e)}"

        finally:
            if 'browser' in locals():
                browser.close()


def main():
    print("Starting HKEX scraping...")
    result = get_last_updated_date()
    print(f"Last Updated Date: {result}")


if __name__ == "__main__":
    main()
