from requests.adapters import HTTPAdapter, Retry
from requests import Session

retries = Retry(total=5, backoff_factor=1, status_forcelist=[502, 503, 504])
session = Session()  # reuse tcp connection
session.mount("http://", HTTPAdapter(max_retries=retries))
session.mount("https://", HTTPAdapter(max_retries=retries))

session.get("https://example.com", timeout=5)  # seconds


import requests
from tenacity import retry, stop_after_attempt, wait_fixed


class Requester:
    def __init__(self):
        self.session = requests.Session()
        # You can set additional parameters for the session here, like headers or authentication

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
    def make_request_with_retry(self, url):
        try:
            response = self.session.get(url, timeout=5)  # Timeout set to 5 seconds
            response.raise_for_status()  # Raises a HTTPError for unsuccessful status codes
            return response
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            raise


if __name__ == "__main__":
    requester = Requester()
    url = "https://example.com"
    response = requester.make_request_with_retry(url)
    if response:
        print("Success:", response.status_code)
