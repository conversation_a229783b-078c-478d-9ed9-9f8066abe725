import multiprocessing
import multiprocessing.managers
import os
import sys
from typing import AnyStr, Union


class QueueManager(multiprocessing.managers.BaseManager):
    def get_queue(
        self, ident: Union[AnyStr, int, type(None)] = None
    ) -> multiprocessing.Queue:
        pass


delattr(QueueManager, "get_queue")


def init_queue_manager_client():
    if not hasattr(QueueManager, "get_queue"):
        QueueManager.register("get_queue")


def main():
    init_queue_manager_client()

    manager: QueueManager = QueueManager(
        sys.argv[1], authkey=QueueManager.__name__.encode()
    )
    manager.connect()

    message = f"A message from {os.getpid()}"
    print(f"Message to send: {message}")
    manager.get_queue().put(message)


if __name__ == "__main__":
    main()
