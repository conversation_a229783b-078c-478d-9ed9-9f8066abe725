import subprocess
import sys

import atexit
import signal

from multiprocessing import Process, Queue
import os
import time
import random


def writer(queue, msg):
    print("Put %s to queue..." % msg)
    queue.put(msg)
    time.sleep(random.random())


class ShellCmdError(Exception):
    pass


# 读数据
def reader(queue):
    while True:
        if not queue.empty():
            value = queue.get(True)
            print("Get %s from queue..." % value)
            # install(value)
            print("exec install ")
            with subprocess.Popen(
                f"echo '{value}'>> /tmp/12",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True,
                universal_newlines=True,
            ) as cmd_process:
                _, err = cmd_process.communicate(timeout=None)
            if err:
                raise ShellCmdError(err)
            time.sleep(1)


def daemonize(pidfile, *, stdin="/dev/null", stdout="/dev/null", stderr="/dev/null"):
    if os.path.exists(pidfile):
        raise RuntimeError("Already running")

    # First fork (detaches from parent)
    try:
        if os.fork() > 0:
            raise SystemExit(0)  # Parent exit
    except OSError:
        raise RuntimeError("fork #1 failed.")

    os.chdir("/")
    os.umask(0)
    os.setsid()
    # Second fork (relinquish session leadership)
    try:
        if os.fork() > 0:
            raise SystemExit(0)
    except OSError:
        raise RuntimeError("fork #2 failed.")

    # Flush I/O buffers
    sys.stdout.flush()
    sys.stderr.flush()

    # Replace file descriptors for stdin, stdout, and stderr
    with open(stdin, "rb", 0) as file_obj:
        os.dup2(file_obj.fileno(), sys.stdin.fileno())
    with open(stdout, "ab", 0) as file_obj:
        os.dup2(file_obj.fileno(), sys.stdout.fileno())
    with open(stderr, "ab", 0) as file_obj:
        os.dup2(file_obj.fileno(), sys.stderr.fileno())

    # Write the PID file
    with open(pidfile, "w") as file_obj:
        print(os.getpid(), file=file_obj)

    # Arrange to have the PID file removed on exit/signal
    atexit.register(lambda: os.remove(pidfile))

    # Signal handler for termination (required)
    def sigterm_handler(signo, frame):
        raise SystemExit(1)

    signal.signal(signal.SIGTERM, sigterm_handler)


# def install(filename):
#     # from apt.cache import Cache
#     # from apt.debfile import DebPackage
#     #
#     # from result import failed, succeed
#     try:
#         cache = Cache()
#         deb_package = DebPackage(filename, cache)
#
#         result = deb_package.install()
#         if 0 == result:
#             print(succeed("安装成功！"))
#         else:
#             print(failed("安装失败"))
#     except Exception as e:
#         print(failed(str(e)))


def main():
    sys.stdout.write("Daemon started with pid {}\n".format(os.getpid()))
    while True:
        sys.stdout.write("Daemon Alive! {}\n".format(time.ctime()))
        time.sleep(10)


if __name__ == "__main__":
    PIDFILE = "/tmp/daemon.pid"

    QUEUE = Queue()

    if len(sys.argv) == 3 or len(sys.argv) == 2:
        pass
    else:
        print("Usage: {} [start|stop|install]".format(sys.argv[0]), file=sys.stderr)
        raise SystemExit(1)

    if sys.argv[1] == "start":
        try:
            daemonize(PIDFILE, stdout="/tmp/daemon.log", stderr="/tmp/dameon.log")
            # 读进程
            pr = Process(target=reader, args=(QUEUE,))
            pr.start()
            pr.join()

        except RuntimeError as e:
            print(e, file=sys.stderr)
            raise SystemExit(1)

        main()

    elif sys.argv[1] == "stop":
        if os.path.exists(PIDFILE):
            with open(PIDFILE) as f:
                os.kill(int(f.read()), signal.SIGTERM)
                print("(%s) End" % os.getpid())
        else:
            print("Not running", file=sys.stderr)
            raise SystemExit(1)

    elif sys.argv[1] == "install":
        if os.path.exists(PIDFILE):
            with open(PIDFILE) as f:
                pass
        else:
            print("Not running", file=sys.stderr)
            raise SystemExit(1)

    elif sys.argv[1] == "writer":
        if os.path.exists(PIDFILE):
            with open(PIDFILE) as f:
                # 使用阻塞模式创建进程，这样就不需要再reader中使用死循环了，可以等write执行完成后，再用reader
                print("写入： " + sys.argv[2])
                pw = Process(
                    target=writer,
                    args=(
                        QUEUE,
                        sys.argv[2],
                    ),
                )
                # 写进程
                pw.start()
                pw.join()

        else:
            print("Not running", file=sys.stderr)
            raise SystemExit(1)

    else:
        print("Unknown command {!r}".format(sys.argv[1]), file=sys.stderr)
        print("(%s) End" % os.getpid())
        raise SystemExit(1)
