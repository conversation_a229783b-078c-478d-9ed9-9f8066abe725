import ctypes
import sys

from PyQt5 import QtWidgets
from PyQt5.QtWidgets import QWidget


class MainWindow(QtWidgets.QWidget):
    def __new__(cls, *args, **kwargs):
        cls._instance = QWidget.__new__(cls, *args, **kwargs)
        print(id(cls._instance), "我被创建了")
        return cls._instance

    def __init__(self):
        super(MainWindow, self).__init__()
        self.resize(500, 300)
        print(id(self), "创建成功了")

    def __del__(self):
        print(id(self), "我被删除了")


def get_value_in_memory(address):
    get_value = ctypes.cast(address, ctypes.py_object).value
    return get_value


# 可在继承类中定义其他绑定事件及其对应的函数
# 主窗口通过按钮显示子窗口
if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    mws = []
    for i in range(0, 2):
        mw = MainWindow()
        mw.show()
    sys.exit(app.exec())
