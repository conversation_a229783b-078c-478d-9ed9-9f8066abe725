import asyncio
import concurrent.futures
import multiprocessing
import multiprocessing.managers
import sys
import threading
from typing import Any, AnyStr, Dict, Union


class QueueManager(multiprocessing.managers.BaseManager):
    def get_queue(
        self, ident: Union[AnyStr, int, type(None)] = None
    ) -> multiprocessing.Queue:
        pass


def get_queue(ident: Union[AnyStr, int, type(None)] = None) -> multiprocessing.Queue:
    global QUEUE  # pylint: disable=global-statement

    if not ident in QUEUE:
        QUEUE[ident] = multiprocessing.Queue()

    return QUEUE[ident]


QUEUE: Dict[Union[AnyStr, int, type(None)], multiprocessing.Queue] = dict()
delattr(QueueManager, "get_queue")


def init_queue_manager_server():
    if not hasattr(QueueManager, "get_queue"):
        QueueManager.register("get_queue", get_queue)


def serve(address: int, term_ev: threading.Event):
    manager: QueueManager
    with <PERSON>ueManager(authkey=QueueManager.__name__.encode()) as manager:
        print(f"Server address {address}: {manager.address}")

        while not term_ev.is_set():
            try:
                item: Any = manager.get_queue().get(timeout=0.1)
                print(f"Client {address}: {item} from {manager.address}")
            except QUEUE.Empty:
                continue


async def main(count: int):
    init_queue_manager_server()
    term_ev: threading.Event = threading.Event()
    # pylint: disable=consider-using-with
    executor: concurrent.futures.ThreadPoolExecutor = (
        concurrent.futures.ThreadPoolExecutor()
    )

    i: int
    for i in range(count):
        asyncio.ensure_future(
            asyncio.get_running_loop().run_in_executor(executor, serve, i, term_ev)
        )

    # Gracefully shut down
    try:
        await asyncio.get_running_loop().create_future()
    except asyncio.CancelledError:
        term_ev.set()
        executor.shutdown()
        raise


if __name__ == "__main__":
    asyncio.run(main(int(sys.argv[1])))
