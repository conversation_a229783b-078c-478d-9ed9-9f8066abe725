import os
import docx


def remove_headers_from_word_files(folder_path):
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.startswith(".~"):
                continue
            if file.endswith(".docx") or file.endswith(".doc"):
                file_path = os.path.join(root, file)
                try:
                    doc = docx.Document(file_path)
                    sections = doc.sections
                    for section in sections:
                        section.header.clear()
                    doc.save(file_path)
                    print(f"已删除文件 {file_path} 的页眉")
                except Exception as e:
                    print(f"处理文件 {file_path} 时发生错误: {e}")


folder_path = '/Users/<USER>/Downloads/02.托班各类合同'
remove_headers_from_word_files(folder_path)
