// ==UserScript==
// @name         FileID Extractor
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  Extract fileid from the current page URL and generate sync commands
// <AUTHOR> Name
// @match        https://gitpd.paodingai.com/*/issues/*
// @grant        GM_addStyle
// @grant        GM_setClipboard
// ==/UserScript==

(function() {
    'use strict';

    function extractFileId(url) {
        const match = url.match(/[?&]fileId=(\d+)/);
        return match ? match[1] : null;
    }

    // Generate sync command text
    function generateSyncCommand(fileId) {
        return `inv sync.file ${fileId}`;
    }

    // Find all links in the document
    const links = document.querySelectorAll('a');


    // Generate sync commands for each fileid
    const syncCommands = [];
    links.forEach(link => {
        const fileId = extractFileId(link.href);
        console.log(link.href);
        if (fileId) {
            syncCommands.push(generateSyncCommand(fileId));
        }
    });

    // Add a button to the page
    function addButton() {
        const descContainer = document.querySelector('.detail-page-description');
        const fixMenu = document.createElement('div');
        fixMenu.classList.add("issue-sticky-header", "gl-fixed");

        const saviorBox = document.createElement('div');
        saviorBox.classList.add("issue-sticky-header-text", "gl-mx-auto", "savior");

        // Output the generated sync commands
        const syncCommandsText = syncCommands.join('\n');
        const syncCommandsContainer = document.createElement('div');
        syncCommandsContainer.style.marginTop = '10px';
        syncCommandsContainer.textContent = syncCommandsText;
        saviorBox.appendChild(syncCommandsContainer);

        // Button to copy sync commands to clipboard
        const copyButton = document.createElement('button');
        copyButton.style='position:absolute;left:100%;top:180px;width:150px';
        copyButton.textContent = '一键复制同步命令';
        copyButton.addEventListener('click', function() {
                const links = document.querySelectorAll('a');


            // Generate sync commands for each fileid
            const syncCommands = [];
            links.forEach(link => {
                const fileId = extractFileId(link.href);
                if (fileId) {
                    syncCommands.push(generateSyncCommand(fileId));
                }
            });
            const syncCommandsText = syncCommands.join('\n');
            GM_setClipboard(syncCommandsText, 'text');
            alert('同步命令已复制到剪贴板');
        });

        saviorBox.appendChild(copyButton);
        fixMenu.appendChild(saviorBox);
        descContainer.appendChild(fixMenu);
    }

    // Add styles
    GM_addStyle(`
        button {
            padding: 10px;
            background-color: #007bff;
            color: #fff;
            border: none;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }

        .sync-commands {
            white-space: pre-line;
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }
    `);

    // Add the button when the page is loaded
    window.addEventListener('load', addButton);

})();
