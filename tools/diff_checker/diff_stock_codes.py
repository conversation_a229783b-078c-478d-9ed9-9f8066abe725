import csv
import re
import argparse


def find_stock_column(headers):
    for i, header in enumerate(headers):
        if re.match(r'(?i)stock_code', header.strip()):
            return i
    return None


def read_stock_codes(file_path, column=None):
    with open(file_path, 'r', newline='') as f:
        reader = csv.reader(f)
        headers = next(reader)
        if column is None:
            col_index = find_stock_column(headers)
            if col_index is None:
                raise ValueError(f"Stock code column not found in {file_path}")
        else:
            if column in headers:
                col_index = headers.index(column)
            else:
                raise ValueError(f"Specified column '{column}' not found in {file_path}")
        return {row[col_index].strip() for row in reader if row}


def compare_stocks(file1, file2, col1=None, col2=None, output='diff_results.csv'):
    stocks1 = read_stock_codes(file1, col1)
    stocks2 = read_stock_codes(file2, col2)
    only_in1 = stocks1 - stocks2
    only_in2 = stocks2 - stocks1
    common = stocks1 & stocks2
    with open(output, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Category', 'Stock Code'])
        for code in sorted(only_in1):
            writer.writerow([f'Only in {file1}', code])
        for code in sorted(only_in2):
            writer.writerow([f'Only in {file2}', code])
        for code in sorted(common):
            writer.writerow(['Common', code])
    print(f"Differences written to {output}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Compare stock codes from two CSV files.')
    parser.add_argument('file1', help='First CSV file')
    parser.add_argument('file2', help='Second CSV file')
    parser.add_argument('--col1', help='Stock code column in first file')
    parser.add_argument('--col2', help='Stock code column in second file')
    parser.add_argument('--output', default='diff_results.csv', help='Output CSV file')
    args = parser.parse_args()
    compare_stocks(args.file1, args.file2, args.col1, args.col2, args.output)
