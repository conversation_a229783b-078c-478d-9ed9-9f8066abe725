import re
from typing import Literal

import docx
from pydantic import BaseModel, Field

from utils import clean_txt
from utils.PatternCollection import PatternCollection

P_CUT = PatternCollection(
    r"(?P<dst>^\d.*\n([\w\.\s、“”\d。,，（）→%-/]+)答案[:：].*$)", re.MULTILINE
)
P_SINGAL = PatternCollection(r"答案：\s?(?P<dst>[A-D]{1})")
P_MULTI = PatternCollection(r"答案：\s?(?P<dst>[A-G]{2,})")
P_QUESTION_TEXT = PatternCollection(r"(?P<dst>^\d.*)A")
P_OPTION = PatternCollection(
    [
        r"(?P<dst>[A-G]\.[A-Z]?[\u4e00-\u9fa5，“”、,，\s\d（）→%-/]+)",
        r"(?P<dst>[A-G]\.\d+[\u4e00-\u9fa5]+)",
        r"(?P<dst>[A-G]\.\d+)",
    ]
)


class DescriptionHistorySchema(BaseModel):
    question_type: Literal["单选题", "多选题"]
    question: str
    score: int = 10
    options_a: str
    options_b: str
    options_c: str
    options_d: str
    options_e: str
    options_f: str
    options_g: str
    options_h: str
    correct_answer: str
    description: str


def get_words_from_docs():
    document = docx.Document("题库.docx")

    full_text = []
    for para in document.paragraphs:
        full_text.append(para.text)

    return "\n".join(full_text)


def print_item(item):
    print("")
    if item.question_type == "单选题":
        print(f"{item.question} （3分）【单选题】")
        print(
            f"{item.options_a}{'（正确答案）' if item.options_a.startswith(item.correct_answer) else ''}"
        )
        print(
            f"{item.options_b}{'（正确答案）' if item.options_b.startswith(item.correct_answer) else ''}"
        )
        print(
            f"{item.options_c}{'（正确答案）' if item.options_c.startswith(item.correct_answer) else ''}"
        )
        print(
            f"{item.options_d}{'（正确答案）' if item.options_d.startswith(item.correct_answer) else ''}"
        )

    else:
        print(f"{item.question} （4分）【多选题】")
        print(item.options_a)
        print(item.options_b)
        print(item.options_c)
        print(item.options_d)
        if item.options_e:
            print(item.options_e)
        if item.options_f:
            print(item.options_f)
        if item.options_g:
            print(item.options_g)
        if item.options_h:
            print(item.options_h)
        print(f"正确答案：{item.correct_answer}")


def print_data(data):
    for item in data:
        print("")
        print_item(item)


def main():
    content = get_words_from_docs()
    questions = []
    for matcher in P_CUT.finditer(content):
        questions.append(clean_txt(matcher.group("dst")))
    # print(len(questions))
    data = []
    for question in questions:
        question_type = "单选题"
        correct_answer = "A"
        multi_matcher = P_MULTI.nexts(question)
        singal_matcher = P_SINGAL.nexts(question)
        if multi_matcher:
            question_type = "多选题"
            correct_answer = multi_matcher.group("dst")
        else:
            if singal_matcher:
                question_type = "单选题"
                correct_answer = singal_matcher.group("dst")
        question_text_matcher = P_QUESTION_TEXT.nexts(question)
        if not question_text_matcher:
            print("wrong**********")
            print(question)
            continue
        question_text = question_text_matcher.group("dst")
        option_matchers = P_OPTION.finditer(question)
        options = []
        for matcher in option_matchers:
            option = matcher.group("dst")
            option = re.sub(r"答案$", "", option)
            options.append(option)
        options = {chr(ord("A") + i): options[i] for i in range(len(options))}
        desc = DescriptionHistorySchema(
            question_type=question_type,
            question=question_text,
            options_a=options["A"],
            options_b=options["B"],
            options_c=options["C"],
            options_d=options["D"],
            options_e=options.get("E", ""),
            options_f=options.get("F", ""),
            options_g=options.get("G", ""),
            correct_answer=correct_answer,
            description="",
        )
        data.append(desc)
        print_item(desc)

    print(len(data))
    # print_data(data)


if __name__ == "__main__":
    main()
