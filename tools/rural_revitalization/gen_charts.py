from datetime import datetime

import pandas as pd

# file_path = '/Users/<USER>/Downloads/巩固脱贫攻坚成果有效衔接乡村振兴_数据详情表_1694508737550.xlsx'
file_path = "/Users/<USER>/Downloads/巩固脱贫攻坚成果有效衔接乡村振兴_数据详情表_169459475831-0913.xlsx"
# file_path1 = '/Users/<USER>/Downloads/巩固脱贫攻坚成果有效衔接乡村振兴_数据详情表_1694508737550_only-0912.xlsx'
# file_path1 = "/Users/<USER>/Downloads/巩固脱贫攻坚成果有效衔接乡村振兴_数据详情表_1694516676072.xlsx"
output_file_path = "/Users/<USER>/Downloads/单位姓名统计.xlsx"


def read_from_pandas():
    df = pd.read_excel(file_path, skiprows=2)
    # 使用 groupby 进行分组，并使用 count 函数计算每个人在单位内的出现次数
    grouped = df.groupby(["单位", "姓名"]).size().reset_index(name="次数")

    # 对结果进行排序，确保相同单位的行挨着
    grouped = grouped.sort_values(by=["单位"])

    # 保存结果到 Excel 文件
    grouped.to_excel("/Users/<USER>/Downloads/单位姓名统计111.xlsx", index=False)


def read_from_pandas_filter(start_date=None, end_date=None):
    df = pd.read_excel(file_path, skiprows=2)

    df["进入时间"] = pd.to_datetime(df["进入时间"]).dt.date

    # 指定时间范围
    if not start_date:
        current_time = datetime.now()
        start_date = current_time.strftime("%Y-%m-%d")
        start_date = f"{start_date} 0:0:0"

    if not end_date:
        current_time = datetime.now()
        end_date = current_time.strftime("%Y-%m-%d %H:%M:%S")

    start_date = pd.to_datetime(start_date).date()
    end_date = pd.to_datetime(end_date).date()

    # 使用筛选条件选择指定时间范围内的数据
    filtered_df = df[(df["进入时间"] >= start_date) & (df["进入时间"] <= end_date)]

    # 使用 groupby 进行分组，并使用 count 函数计算每个人在单位内的出现次数
    grouped = filtered_df.groupby(["单位", "姓名"]).size().reset_index(name="次数")

    # 对结果进行排序，确保相同单位的行挨着
    grouped = grouped.sort_values(by=["单位"])

    # 保存结果到 Excel 文件
    grouped.to_excel(output_file_path, index=False)


def stat_from_oppyxl():
    pass


def read_from_pandas_filter1(file_path, start_date=None, end_date=None):
    df = pd.read_excel(file_path, skiprows=2)

    df["进入时间"] = pd.to_datetime(df["进入时间"]).dt.date

    # 指定时间范围
    if not start_date:
        current_time = datetime.now()
        start_date = current_time.strftime("%Y-%m-%d")
        start_date = f"{start_date} 0:0:0"

    if not end_date:
        current_time = datetime.now()
        end_date = current_time.strftime("%Y-%m-%d %H:%M:%S")

    start_date = pd.to_datetime(start_date).date()
    end_date = pd.to_datetime(end_date).date()

    # 使用筛选条件选择指定时间范围内的数据
    filtered_df = df[(df["进入时间"] >= start_date) & (df["进入时间"] <= end_date)]
    return filtered_df


def agg_data(files, output_file_path, start_date=None, end_date=None):
    empty_df = pd.DataFrame()
    dfs = []
    for file in files:
        df = read_from_pandas_filter1(file, start_date, end_date)
        dfs.append(df)

    merged_df = pd.concat(dfs, axis=0)

    # 使用 groupby 进行分组，并使用 count 函数计算每个人在单位内的出现次数
    grouped = merged_df.groupby(["单位", "姓名"]).size().reset_index(name="次数")

    # 对结果进行排序，确保相同单位的行挨着
    grouped = grouped.sort_values(by=["单位"])
    grouped.to_excel(output_file_path, index=False)


def main():
    read_from_pandas()
    # read_from_pandas_filter()
    # stat_from_oppyxl()
    # files = [
    #     '/Users/<USER>/Downloads/巩固脱贫攻坚成果有效衔接乡村振兴_数据详情表_1694508737550.xlsx',
    #     '/Users/<USER>/Downloads/巩固脱贫攻坚成果有效衔接乡村振兴_数据详情表_1694508737550_only-0912.xlsx',
    # ]
    # agg_data(files, '/Users/<USER>/Downloads/test.xlsx')


if __name__ == "__main__":
    main()
