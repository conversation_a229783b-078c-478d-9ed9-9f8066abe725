import qrcode

# 创建一个QRCode对象
qr = qrcode.QRCode(
    version=1,
    error_correction=qrcode.constants.ERROR_CORRECT_L,
    box_size=10,
    border=4,
)

# 添加要编码的数据
data = "https://ks.longyixiu.cn/?questionId=0b98f3J%2FbZxbESfiUWXFLA%3D%3D#"  # 替换为你的URL或其他数据
qr.add_data(data)
qr.make(fit=True)

# 创建QR码图像
qr_img = qr.make_image(fill_color="black", back_color="white")
from PIL import Image, ImageDraw

# 创建一个白色的背景图像，大小与二维码相同
size = qr_img.size
circle_img = Image.new("RGB", size, "white")

# 创建一个与背景图像相同大小的Mask，表示圆形区域
mask = Image.new("L", size, 0)
draw = ImageDraw.Draw(mask)
draw.ellipse((0, 0, size[0], size[1]), fill=255)

# 在背景图像上应用Mask，实现圆形效果
circle_img.putalpha(mask)

# 将二维码图像粘贴到圆形背景图像中心
circle_img.paste(qr_img, (0, 0), qr_img)

# 保存或显示圆形二维码图像
circle_img.save("circle_qr.png")  # 保存为文件
circle_img.show()  # 显示图像
