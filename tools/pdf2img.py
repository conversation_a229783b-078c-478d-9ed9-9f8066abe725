import fitz
import pypdfium2 as pdfium

from utils import DATA_PATH

# path = "/Users/<USER>/Downloads/相关材料/门诊、急诊发票.pdf"
# path = "/Users/<USER>/Downloads/相关材料/门诊、急诊病历.pdf"
# path = "/Users/<USER>/Downloads/相关材料/门急诊检查报告单.pdf"
PATH = "/Users/<USER>/Downloads/相关材料/剩余图片.pdf"
out_path = DATA_PATH / "pic"

out_path.mkdir(exist_ok=True)


def pdfium_demo():
    pdf = pdfium.PdfDocument(PATH)
    n_pages = len(pdf)
    for page_number in range(n_pages):
        page = pdf.get_page(page_number)
        pil_image = page.render_topil(
            scale=1,
            rotation=0,
            crop=(0, 0, 0, 0),
            colour=(255, 255, 255, 255),
            annotations=True,
            greyscale=False,
            optimise_mode=pdfium.OptimiseMode.NONE,
        )
        pil_image.save(f"image_{page_number+1}.png")


def pymupdf_demo():
    doc = fitz.open(PATH)
    zoom = 4
    mat = fitz.Matrix(zoom, zoom)
    count = 0
    # Count variable is to get the number of pages in the pdf
    for _ in doc:
        count += 1
    for i in range(count):
        val = out_path / f"image_{i + 1}.png"
        page = doc.load_page(i)
        pix = page.get_pixmap(matrix=mat)
        pix.save(val)
    doc.close()


def main():
    pymupdf_demo()


if __name__ == "__main__":
    main()
