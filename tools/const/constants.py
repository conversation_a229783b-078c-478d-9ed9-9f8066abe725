class Solution:
    def isValid(self, s):
        left_bracket = "([{"
        right_bracket = "(]}"
        if len(s) / 2 == 1:
            return False
        stack = []
        for item in s:
            if item in left_bracket:
                stack.append(item)
            if item in right_bracket:
                if not stack:
                    return False
                current = stack.pop()
                if item == ")" and current != "(":
                    return False
                if item == "]" and current != "[":
                    return False
                if item == "}" and current != "{":
                    return False
        return stack == []


if __name__ == "__main__":
    s = Solution()
    print(s.isValid("((())])"))
