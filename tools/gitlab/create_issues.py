#!/usr/bin/env python3
"""
GitLab 批量创建 Issues 脚本

使用方法:
    python create_issues.py              # 测试模式，只显示将要创建的 issues
    python create_issues.py --execute    # 实际执行，创建 issues
"""

import requests
import argparse


# ==================== 配置区域 ====================
GITLAB_URL = "https://gitpd.paodingai.com"
PROJECT_PATH = "cheftin/docs_jura"  # 项目路径
ACCESS_TOKEN = "pnikNlu5Hvzzy6uuTVoDWm86MQp1Ojc1CA.01.0y0mb9y0a"
# MILESTONE_NAME = "25/26 年报维保内部测试第一轮"
MILESTONE_NAME = "【25/26维保】模型增强- ESG 数据抽取"

# 需要创建 issue 的规则列表
RULES = [
    'KPI A1.1 - emission type and data',
    'KPI A1.2 part 1 - Scope 1',
    'KPI A1.2 part 2 - Scope 2',
    'KPI A1.3 - hazardous waste',
    'KPI A1.4 - non-hazardous waste',
    'KPI A2.1 - energy consumption',
    'KPI A2.2 - water consumption',
    'KPI A2.4 part 1 - water sourcing',
    'KPI A2.5 - packaging material',
    'KPI B1.2 - employee turnover by types',
    'KPI B2.1 - work-related fatalities',
    'KPI B2.2 - work injury lost days',
    'KPI B3.1 - percentage of employees trained',
    'KPI B3.2 - training hours completed',
    'KPI B5.1 - number of suppliers',
    'KPI B6.1 - products recall',
    'KPI B6.2 - products related complaints',
    'KPI B7.1 - legal cases on corruption',
    'KPI B4.1 - review measures to avoid child & forced labour',
    'KPI B4.2 - steps to avoid child & forced labour',
    'KPI B6.3 - IP rights protection',
    'KPI B6.4 - quality assurance process',
    'KPI B8.1 - community investment focus',
    'KPI B8.2 - resources contributed',
    'KPI A1.5 - emission target',
    'KPI A1.6 part 2 - waste reduction target',
    'KPI A2.3 - energy efficiency targets',
    'KPI A2.4 part 2 - water efficiency targets',
    'MDR 13 iii - board progress review',
    'A1 policies - emissions',
    'B6 policies - product responsibility',
    'A1 law compliance - emissions',
    'B1 law compliance - employment',
    'B2 law compliance - health and safety',
    'B4 law compliance - labour standards',
    'B6 law compliance - product responsibility',
]

# Issue 标题模板
TITLE_TEMPLATE = "【B】【高优先级】【ESG维保】 {rule} -- 数据抽取"
# ==================== 配置区域结束 ====================


def get_project_id(gitlab_url, project_path, token):
    """
    获取项目的 ID
    """
    # 对项目路径进行 URL 编码
    encoded_path = project_path.replace("/", "%2F")
    url = f"{gitlab_url}/api/v4/projects/{encoded_path}"
    headers = {"PRIVATE-TOKEN": token}

    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()["id"]
    else:
        raise Exception(f"获取项目 ID 失败: {response.status_code}, {response.text}")


def get_milestone_id(gitlab_url, project_id, milestone_name, token):
    """
    获取 milestone 的 ID
    """
    url = f"{gitlab_url}/api/v4/projects/{project_id}/milestones"
    headers = {"PRIVATE-TOKEN": token}

    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        milestones = response.json()
        for milestone in milestones:
            if milestone["title"] == milestone_name:
                return milestone["id"]
        raise Exception(f"未找到名为 '{milestone_name}' 的 milestone")
    else:
        raise Exception(f"获取 milestone 失败: {response.status_code}, {response.text}")


def create_issue(gitlab_url, project_id, title, milestone_id, token, dry_run=True):
    """
    创建一个 issue
    """
    if dry_run:
        print(f"  [测试] 将创建 Issue: {title}")
        return True

    url = f"{gitlab_url}/api/v4/projects/{project_id}/issues"
    headers = {"PRIVATE-TOKEN": token}
    data = {
        "title": title,
        "milestone_id": milestone_id,
    }

    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 201:
        issue_url = response.json()["web_url"]
        print(f"  ✓ 创建成功: {title}")
        print(f"    链接: {issue_url}")
        return True
    else:
        print(f"  ✗ 创建失败: {title}")
        print(f"    错误: {response.status_code}, {response.text}")
        return False


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="批量创建 GitLab Issues")
    parser.add_argument("--execute", action="store_true", help="实际执行创建操作（不加此参数则为测试模式）")
    args = parser.parse_args()

    dry_run = not args.execute

    # 显示模式
    if dry_run:
        print("=" * 60)
        print("🧪 测试模式（不会真正创建 issues）")
        print("=" * 60)
    else:
        print("=" * 60)
        print("🚀 执行模式（将实际创建 issues）")
        print("=" * 60)

    try:
        # 1. 获取项目 ID
        print(f"\n1️⃣  获取项目 ID...")
        project_id = get_project_id(GITLAB_URL, PROJECT_PATH, ACCESS_TOKEN)
        print(f"   项目 ID: {project_id}")

        # 2. 获取 milestone ID
        print(f"\n2️⃣  获取 Milestone ID...")
        milestone_id = get_milestone_id(GITLAB_URL, project_id, MILESTONE_NAME, ACCESS_TOKEN)
        print(f"   Milestone: {MILESTONE_NAME} (ID: {milestone_id})")

        # 3. 批量创建 issues
        print(f"\n3️⃣  创建 Issues (共 {len(RULES)} 个)...")
        success_count = 0

        for rule in RULES:
            title = TITLE_TEMPLATE.format(rule=rule)
            if create_issue(GITLAB_URL, project_id, title, milestone_id, ACCESS_TOKEN, dry_run):
                success_count += 1

        # 4. 显示结果
        print("\n" + "=" * 60)
        if dry_run:
            print(f"✅ 测试完成！预计将创建 {success_count}/{len(RULES)} 个 issues")
            print(f"\n如需实际创建，请运行: python create_issues.py --execute")
        else:
            print(f"✅ 执行完成！成功创建 {success_count}/{len(RULES)} 个 issues")
        print("=" * 60)

    except Exception as e:
        print(f"\n❌ 错误: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
