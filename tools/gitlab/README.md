# GitLab 批量创建 Issues 工具

## 功能说明

这个脚本用于在 GitLab 上批量创建 issues，特别适合需要创建大量相似格式的 issues 的场景。

## 使用步骤

### 1. 安装依赖

```bash
pip install requests
```

或者如果你的项目使用 uv：

```bash
uv pip install requests
```

### 2. 测试模式（推荐先运行）

测试模式不会实际创建 issues，只会显示将要创建的内容：

```bash
cd tools/gitlab
python create_issues.py
```

### 3. 实际执行

确认测试输出正确后，执行实际创建：

```bash
python create_issues.py --execute
```

## 配置说明

如果需要修改配置，编辑 `create_issues.py` 文件中的配置区域：

```python
# ==================== 配置区域 ====================
GITLAB_URL = "https://gitpd.paodingai.com"
PROJECT_PATH = "cheftin/docs_jura"
ACCESS_TOKEN = "你的access_token"
MILESTONE_NAME = "25/26 年报维保内部测试第一轮"

# 需要创建 issue 的规则列表
RULES = [
    "A10.1", "A10.2", ...
]

# Issue 标题模板
TITLE_TEMPLATE = "【B】【高优先级】年报维保 {rule}-- 数据抽取第一轮测试"
# ==================== 配置区域结束 ====================
```

## 输出示例

### 测试模式输出：
```
============================================================
🧪 测试模式（不会真正创建 issues）
============================================================

1️⃣  获取项目 ID...
   项目 ID: 12345

2️⃣  获取 Milestone ID...
   Milestone: 25/26 年报维保内部测试第一轮 (ID: 67)

3️⃣  创建 Issues (共 23 个)...
  [测试] 将创建 Issue: 【B】【高优先级】年报维保 A10.1-- 数据抽取第一轮测试
  [测试] 将创建 Issue: 【B】【高优先级】年报维保 A10.2-- 数据抽取第一轮测试
  ...

============================================================
✅ 测试完成！预计将创建 23/23 个 issues

如需实际创建，请运行: python create_issues.py --execute
============================================================
```

## 注意事项

1. ⚠️ **先在测试模式下运行**，确认输出正确后再执行实际创建
2. ⚠️ **Access Token 请妥善保管**，不要提交到公开仓库
3. ⚠️ 确保 Milestone 名称在 GitLab 项目中已存在
4. ⚠️ 确保 Access Token 有创建 issues 的权限
