import re

import fitz  # PyMuPDF


def extract_pdf_hyperlinks(pdf_path):
    """
    使用PyMuPDF从PDF中提取超链接，并尝试从上下文获取描述性文本

    参数:
    pdf_path (str): PDF文件的完整路径

    返回:
    list: 包含Markdown格式超链接的列表
    """
    # 存储超链接的列表
    hyperlinks = []

    try:
        # 打开PDF文件
        pdf_document = fitz.open(pdf_path)

        # 遍历每一页
        for page_num in range(len(pdf_document)):
            # 获取当前页
            page = pdf_document[page_num]
            print(page_num)
            # 提取页面的链接
            links = page.get_links()

            # 处理每个链接
            for link in links:
                # 检查链接类型为外部链接
                if link['kind'] == fitz.LINK_URI:
                    url = link['uri']

                    # 验证链接格式
                    if re.match(r'^https?://\S+$', url):
                        # 尝试从URL中提取描述性文本
                        try:
                            # 从URL路径中提取描述
                            path_parts = url.split('/')
                            # 移除空字符串和常见域名
                            path_parts = [
                                part
                                for part in path_parts
                                if part
                                and part not in ['www.mtr.com.hk', 'sustainability', 'en', 'archive', 'corporate']
                            ]

                            # 创建描述性文本
                            if path_parts:
                                # 使用最后一个路径部分作为基础
                                desc = path_parts[-1].replace('-', ' ').replace('_', ' ')
                                # 移除文件扩展名
                                desc = re.sub(r'\.(html|pdf|htm)$', '', desc)
                            else:
                                # 如果无法从路径提取，使用域名
                                desc = re.sub(r'^https?://|www\.|\.com$', '', url.split('/')[2])

                            # 首字母大写
                            desc = desc.capitalize()

                            # 创建Markdown格式链接
                            md_link = f"[{desc}]({url})"
                            print(md_link)

                        except Exception:
                            # 如果提取描述失败，使用默认描述
                            md_link = f"[网页链接]({url})"

                        # 避免重复链接
                        if md_link not in hyperlinks:
                            hyperlinks.append(md_link)

        # 关闭PDF文件
        pdf_document.close()

    except Exception as e:
        print(f"提取PDF链接时出错: {e}")

    return hyperlinks


def main():
    # PDF文件路径
    pdf_path = '/Users/<USER>/Downloads/2024-04-11T17-30_00066_MTR-CORPORATION-LIMITED_Sustainability-Report-2023.pdf'

    # 提取超链接
    links = extract_pdf_hyperlinks(pdf_path)

    # 打印每个超链接
    print("PDF中的超链接：")
    for link in links:
        print(link)


if __name__ == "__main__":
    main()
