import xlrd
from openpyxl.reader.excel import load_workbook
from openpyxl.workbook import Workbook

from utils import DATA_PATH

origin_questions_file = DATA_PATH / "files" / "origin_questions.xlsx"
OUTPUT_FILE = "/Users/<USER>/Downloads/安全保卫部题库.xlsx"
template_header = [
    "题型",
    "题目标题",
    "题干说明",
    "是否必填",
    "选项1(A)",
    "选项2(B)",
    "选项3(C)",
    "选项4(D)",
    "选项5(E)",
    "选项6(F)",
    "分值(最小单位1分)",
    "正确答案",
    "答案解析",
]


def main():
    # load data
    workbook = load_workbook(origin_questions_file)
    worksheet = workbook.active
    data = [template_header]
    for row in worksheet.iter_rows(min_row=3):
        if not row[4].value:
            break
        data.append(
            [
                f"{row[4].value}题",
                row[5].value,
                "",
                "",
                row[6].value,
                row[7].value,
                row[8].value,
                row[9].value,
                row[10].value,
                row[11].value,
                "",
                correct_answer(row[12].value),
                row[13].value,
            ]
        )

    # write to anthor xlsx
    workbook = Workbook()
    worksheet = workbook.active
    for item in data:
        worksheet.append(item)
    workbook.save(OUTPUT_FILE)


def correct_answer(value):
    if not value:
        return ""
    return value.replace("-", "")


def main1():
    # load data
    origin_questions_file = DATA_PATH / "files" / "知识竞赛题库.xls"
    work_book = xlrd.open_workbook(origin_questions_file)
    for idx in range(4):
        work_sheet = work_book.sheet_by_index(idx)
        if idx == 0:
            template_header = [
                "题型",
                "题目标题",
                "题干说明",
                "是否必填",
                "选项1(A)",
                "选项2(B)",
                "选项3(C)",
                "选项4(D)",
                "分值(最小单位1分)",
                "正确答案",
                "答案解析",
            ]
        if idx == 1:
            template_header = [
                "题型",
                "题目标题",
                "题干说明",
                "是否必填",
                "选项1(A)",
                "选项2(B)",
                "选项3(C)",
                "选项4(D)",
                "选项5(E)",
                "选项6(F)",
                "选项7(G)",
                "选项8(H)",
                "分值(最小单位1分)",
                "正确答案",
                "答案解析",
            ]
        if idx == 2:
            template_header = [
                "题型",
                "题目标题",
                "题干说明",
                "是否必填",
                "选项1(A)",
                "选项2(B)",
                "分值(最小单位1分)",
                "正确答案",
                "答案解析",
            ]
        if idx == 3:
            template_header = [
                "题型",
                "题目标题",
                "题干说明",
                "是否必填",
                "分值(最小单位1分)",
                "正确答案",
                "答案解析",
            ]
        data = [template_header]
        question_name = work_sheet.name
        if question_name == "问答题":
            question_name = "简答题"
        rows = work_sheet.get_rows()
        for row in rows:
            if row[0].value == "部门":
                continue
            if not row[0].value:
                break
            if idx == 0:
                data_item = [
                    question_name,
                    row[1].value,
                    row[0].value,
                    "",
                    row[2].value,
                    row[3].value,
                    row[4].value,
                    row[5].value,
                    # row[6].value,
                    # row[7].value,
                    "",
                    correct_answer(row[10].value),
                ]
            if idx == 1:
                data_item = [
                    question_name,
                    row[1].value,
                    row[0].value,
                    "",
                    row[2].value,
                    row[3].value,
                    row[4].value,
                    row[5].value,
                    row[6].value,
                    row[7].value,
                    row[8].value,
                    row[9].value,
                    "",
                    correct_answer(row[10].value),
                ]
            if idx == 2:
                data_item = [
                    question_name,
                    row[1].value,
                    row[0].value,
                    "",
                    "正确",
                    "错误",
                    "",
                    "A" if row[2].value == "正确" else "B",
                ]
            if idx == 3:
                data_item = [
                    question_name,
                    row[2].value,
                    row[1].value,
                    "",
                    row[0].value,
                    row[3].value,
                ]
            data.append(data_item)

        output_file = f"/Users/<USER>/Downloads/知识竞赛题库-{question_name}-上传.xlsx"
        workbook = Workbook()
        worksheet = workbook.active
        for item in data:
            print(item)
            worksheet.append(item)
        workbook.save(output_file)


if __name__ == "__main__":
    # main()
    main1()
