import os
import re

from openpyxl import load_workbook


def main():
    three_times_pattern = re.compile(r'3次')
    three_vaccine_pattern = re.compile(r'þ三针')
    outside_pattern = re.compile(r'þ是')
    non_work_pattern = re.compile(r'学生|^幼儿$|退休|无')
    party_members = []
    students = []
    work_members = []
    out_persons = []
    three_nucleic_acid = []
    three_vaccine = []
    path = '/data/files/all_files'
    for root_path, sub_path, files in os.walk(path):
        if not files:
            continue
        for file in files:
            if file.startswith('.'):
                continue
            workbook = load_workbook(os.path.join(root_path, file))
            sheet = workbook.active
            rows = list(sheet.rows)
            # k = 11
            name = rows[2][3].value
            if rows[2][10].value == '党员':
                party_members.append(name)
            id_card = rows[3][3].value
            if id_card and '2000' < id_card[6:10] < '2019':
                students.append(name)

            company = rows[3][9].value
            if company and not non_work_pattern.search(re.sub(r'\s+', '', company)):
                work_members.append(name)
            nucleic_desc = rows[7][3].value
            if three_times_pattern.search(re.sub(r'\s+', '', nucleic_desc)):
                three_nucleic_acid.append(name)

            vaccine_desc = rows[8][12].value
            if three_vaccine_pattern.search(re.sub(r'\s+', '', vaccine_desc)):
                three_vaccine.append(name)

            outside_desc = rows[5][1].value
            if outside_pattern.search(re.sub(r'\s+', '', outside_desc)):
                out_persons.append(name)

    print(f'党员数量: {len(party_members)}', party_members)
    print(f'学生数量: {len(students)}', students)
    print(f'外出人数: {len(out_persons)}', out_persons)
    print(f'上班人数: {len(work_members)}', work_members)
    print(f'2022年核酸检测3次以上的人数: {len(three_nucleic_acid)}', three_nucleic_acid)
    print(f'接种三针疫苗人数: {len(three_vaccine)}', three_vaccine)


if __name__ == '__main__':
    main()