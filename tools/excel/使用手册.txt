使用手册
1. 将台账和信息表放到calliper文件夹中
2. 将台账文件名称修改成  7日台账.xlsx
3. 将信息表文件名称修改成 核酸检验信息表.xlsx
4. 如果信息表有密码，需要打开文件手动输入密码之后取消密码保护
    取消密码的步骤
        若您使用的是office
            1.打开文档并输入其密码。
            2.转到"文件>信息>">"使用密码加密"。
            3.清除"密码"框中的密码，然后单击"确定"。
        若您使用的是wps
            1. 点击左上角“文件”，在选项栏中找到“文档加密”
            2.点击“文档加密”下的“密码加密”选项卡
            3.在弹出的菜单中，删除自己所设置的密码，即文档的加密密码，最后点击右下角的“应用”即可
5. 双击calliper.py 




以下内容您无需关心
deploy record
```
pip3 install openpyxl --index-url=http://pypi.doubanio.com/simple --trusted-host pypi.doubanio.com
pip3 install parseIdCard --index-url=http://pypi.doubanio.com/simple --trusted-host pypi.doubanio.com


```
