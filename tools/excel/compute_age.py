import datetime
import logging
import re
from pathlib import Path

import attrs
from openpyxl.reader.excel import load_workbook
from openpyxl.workbook import Workbook

date_reg = re.compile(r"(\d.\d\d)")

BASE_PATH = Path("/Users/<USER>/Downloads/")
PARAMETER_PATH = Path("/Users/<USER>/Downloads/") / "test.xlsx"


@attrs.define
class Parameter:
    name: str
    phone: str
    id_card: str


error_data_in_parameter = {}
table_headers = []


def get_data_parameters():
    logging.info("start extract parameters")
    workbook = load_workbook(PARAMETER_PATH)
    sheet = workbook.active
    rows = list(sheet.rows)
    data = []
    for row in rows[:2]:
        table_headers.append([item.value for item in row])
    for row in rows[2:]:
        id_card = str(row[3].value)
        born_year = id_card[6:10]
        age = 2022 - int(born_year)
        data.append(
            (
                str(row[0].value),
                str(row[1].value),
                str(row[2].value),
                str(row[3].value),
                str(row[4].value),
                age,
            )
        )
    return data


def save_diff_data(data):
    ret_file = BASE_PATH / f"test1.xlsx"
    workbook = Workbook()
    sheet = workbook.active
    for header in table_headers:
        sheet.append(header)
    for row in data:
        sheet.append(row)
    workbook.save(ret_file)


def main():
    data = get_data_parameters()
    save_diff_data(data)


if __name__ == "__main__":
    main()
