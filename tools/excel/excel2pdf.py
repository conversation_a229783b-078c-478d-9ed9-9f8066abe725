import openpyxl
from fpdf import FPDF
from openpyxl.workbook import Workbook

EXCEL_PATH = "./000001_广发某产品A_经营业绩月报_202204.xlsx"


def asp(input_file):
    # Load Excel file
    workbook = Workbook(input_file)

    # Convert Excel to PDF
    workbook.save("xlsx-to-pdf.pdf")


def excel_to_pdf(input_file, output_file):
    # Load the Excel file
    workbook = openpyxl.load_workbook(input_file)
    # Select the first sheet
    sheet = workbook.active

    # Create a PDF object
    pdf = FPDF()
    pdf.add_page()  # Add a page

    # Set font and size for the PDF
    pdf.set_font("Arial", size=12)

    # Iterate through each row and column in the sheet
    for row in sheet.iter_rows(values_only=True):
        # Convert each cell value to a string
        row = [str(cell) if cell else "" for cell in row]
        # Join the row values with tabs
        line = "\t".join(row)
        # Add the line to the PDF
        pdf.cell(0, 10, line.encode("latin-1", "replace").decode("latin-1"), ln=True)

    # Save the PDF to a file
    pdf.output(output_file)


# Example usage
if __name__ == "__main__":
    PDF_FILE = "output.pdf"
    # excel_to_pdf(excel_path, pdf_file)
    asp(EXCEL_PATH)
