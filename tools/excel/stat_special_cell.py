import os
import re

from openpyxl import load_workbook
from openpyxl.workbook import Workbook

file_path = "/Users/<USER>/Downloads/test.xlsx"


def main():
    import_res = {}
    thing_res = {}
    workbook = load_workbook(file_path, data_only=True)
    for sheet in workbook.worksheets:
        print(sheet.title)
        rows = list(sheet.rows)
        import_value = rows[6][10].value
        print(import_value)
        thing_value = rows[11][10].value
        print(thing_value)
        import_res[sheet.title] = import_value
        thing_res[sheet.title] = thing_value if thing_value else 0

    print(import_res)
    print(thing_res)
    s = sum(import_res.values())
    s1 = sum(thing_res.values())

    out_file_path = "/Users/<USER>/Downloads/result.xlsx"
    workbook1 = Workbook()
    sheet = workbook1.active
    sheet.append(["时间", "重点人员当日核减", "物品环境"])
    for date, item in import_res.items():
        sheet.append([date, item, thing_res[date]])
    sheet.append(["总和", s, s1])

    workbook1.save(out_file_path)


if __name__ == "__main__":
    main()
