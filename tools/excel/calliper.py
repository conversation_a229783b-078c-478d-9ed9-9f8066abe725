import datetime
import logging
import re
from pathlib import Path

import attrs
from openpyxl.reader.excel import load_workbook
from openpyxl.workbook import Workbook
from parseIdCard import parseIdCard

date_reg = re.compile(r"(\d.\d\d)")

BASE_PATH = Path("/Users/<USER>/Downloads/nat_info")

PARAMETER_PATH = BASE_PATH / "7日台账（9.15）.xlsx"
INFOMATION_PATH = BASE_PATH / "核酸检验信息表-20220915.xlsx"
table_headers = []


@attrs.define
class Parameter:
    name: str
    phone: str
    id_card: str


error_data_in_parameter = {}


def get_data_parameters():
    logging.info("start extract parameters")
    workbook = load_workbook(PARAMETER_PATH)
    sheet = workbook["目前管控"]
    rows = list(sheet.rows)
    data = {}
    for row in rows[:2]:
        table_headers.append([item.value for item in row])
    for row in rows[2:]:
        name = row[5].value
        phone = row[9].value
        id_card = row[7].value
        # todo 比较时间
        # left_time = row[25].value
        left_time1 = row[26].value
        mather = date_reg.search(str(left_time1))
        if mather:
            left_time = mather.group()
            if float(left_time) < 9.15:
                continue
        key = str(id_card).lower().strip()
        # key = f"{name.lower().strip()}_{str(phone).lower().strip()}".strip()
        # key = f"{name.lower().strip()}".strip()
        # key = f"{name.lower().strip()}_{str(phone).lower().strip()}_{str(id_card).lower().strip()}".strip()
        data[key] = [item.value for item in row]
        check_res = parseIdCard.parseIdCard(str(id_card).lower().strip())
        if check_res["code"] == "Error" and check_res["info"] != "未知地区编码":
            error_data_in_parameter[id_card] = [item.value for item in row]

    return data


def get_data_from_information_table():
    logging.info("start extract infomation")
    workbook = load_workbook(INFOMATION_PATH)
    sheet = workbook.active
    rows = list(sheet.rows)
    data = set()
    for row in rows[1:]:
        name = row[0].value
        id_card = row[1].value
        phone = row[4].value
        id_card = str(id_card).lower().strip()
        phone = str(phone).lower().strip()
        name = str(name).lower().strip()
        # key = id_card
        # key = f'{name.lower().strip()}_{str(phone).lower().strip()}'.strip()
        # key = f'{name.lower().strip()}'.strip()
        # key = f'{name.lower().strip()}_{str(phone).lower().strip()}_{id_card}'.strip()
        # data[key] = [item.value for item in row]
        data.add(id_card)
        data.add(phone)
        data.add(name)
    return data


def diff(patameter_data, info_data):
    logging.info("start diff")
    res = []
    for key, item in patameter_data.items():
        keys = key.split("_")
        if all(i not in info_data for i in keys):
            res.append(item)

    return res


def save_diff_data(data):
    logging.info("start save")
    today = datetime.datetime.today()
    today_str = f"{today.year}-{today.month}-{today.day}"
    ret_file = BASE_PATH / f"台账中有 核酸检验信息表里没有-{today_str}.xlsx"
    workbook = Workbook()
    sheet = workbook.active
    for header in table_headers:
        sheet.append(header)
    for row in data:
        sheet.append(row)
    workbook.save(ret_file)


def main():
    # get data from parameters
    data = get_data_parameters()
    info_data = get_data_from_information_table()
    res = diff(data, info_data)
    save_diff_data(res)
    for key in error_data_in_parameter.keys():
        check_res = parseIdCard.parseIdCard(key)
        print(check_res)


if __name__ == "__main__":
    main()
