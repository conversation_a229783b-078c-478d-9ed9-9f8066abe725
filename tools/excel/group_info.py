import re

import pandas as pd
from openpyxl import load_workbook, Workbook

p_zhen = re.compile(r"(?P<dst>\w\w)(镇|乡)")


def sort_rule(row):
    ret = 10
    zhens = ["三泉镇", "北张镇", "泽掌镇", "横桥镇", "横桥乡", "万安镇", "泉掌镇", "阳王镇", "古交镇", "龙兴镇"]
    address = row[4].value
    mather = p_zhen.search(address)
    if mather:
        special_add = mather.group()
        if special_add in zhens:
            ret = zhens.index(special_add)
    if "商贸经济开发区" in address:
        ret = 11
    if "煤化" in address:
        ret = 12
    if "汾河湾" in address:
        ret = 13
    if "商贸经济开发区" in address:
        ret = 14
    if "轻纺工业园" in address:
        ret = 15
    if "经济技术开发区" in address:
        ret = 16
    return ret


def main():
    # read data from excel, then sort data by fifth column
    file_path = "/Users/<USER>/Downloads/运输企业名单.xlsx"
    workbook = load_workbook(file_path)
    sheet = workbook.active
    rows = list(sheet.rows)[1:]
    rows.sort(key=lambda x: sort_rule(x))
    for row in rows:
        print(row[4].value)
    write(rows)


def write(all_rows):
    out_file_path = "/Users/<USER>/Downloads/运输企业名单1.xlsx"
    workbook = Workbook()
    sheet = workbook.active
    sheet.append([])
    for row in all_rows:
        row = [cell.value for cell in row]
        sheet.append(row)
    workbook.save(out_file_path)


if __name__ == "__main__":
    main()
