import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime


def analyze_agm_time(csv_file):
    # 读取CSV文件
    df = pd.read_csv(csv_file)

    # 将published列转换为datetime类型
    df['published'] = pd.to_datetime(df['published'])

    # 按年统计
    year_stats = df['published'].dt.year.value_counts().sort_index()
    print('\n按年份统计：')
    print(year_stats)

    # 按月统计
    month_stats = df['published'].dt.month.value_counts().sort_index()
    print('\n按月份统计：')
    print(month_stats)

    # 按日统计
    day_stats = df['published'].dt.day.value_counts().sort_index()
    print('\n按日期统计：')
    print(day_stats)

    # 按小时统计
    hour_stats = df['published'].dt.hour.value_counts().sort_index()
    print('\n按小时统计：')
    print(hour_stats)

    # 绘制时间分布图
    plt.figure(figsize=(15, 10))

    # 年份分布
    plt.subplot(2, 2, 1)
    year_stats.plot(kind='bar')
    plt.title('年份分布')
    plt.xlabel('年份')
    plt.ylabel('数量')

    # 月份分布
    plt.subplot(2, 2, 2)
    month_stats.plot(kind='bar')
    plt.title('月份分布')
    plt.xlabel('月份')
    plt.ylabel('数量')

    # 日期分布
    plt.subplot(2, 2, 3)
    day_stats.plot(kind='bar')
    plt.title('日期分布')
    plt.xlabel('日期')
    plt.ylabel('数量')

    # 小时分布
    plt.subplot(2, 2, 4)
    hour_stats.plot(kind='bar')
    plt.title('小时分布')
    plt.xlabel('小时')
    plt.ylabel('数量')

    plt.tight_layout()
    plt.show()


if __name__ == '__main__':
    csv_file = 'agm_file_meta.csv'
    analyze_agm_time(csv_file)
