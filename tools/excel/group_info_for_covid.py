import re

import pandas as pd
from openpyxl import load_workbook, Workbook

p_zhen = re.compile(r"(?P<dst>\w\w)(镇|乡)")
zhens_simple = ["三泉", "北张", "泽掌", "横桥", "万安", "泉掌", "阳王", "古交", "龙兴", "社区"]
target_col = 10
write_col = 19
file_name = "涉疫场所暴露人员汇总--202207071002(2)"
file_name = "北环路暴露人员住址及核检（3）"
file_name = "涉疫场所暴露人员汇总-737-202207071002（1）"
# file_name = '（流调组第四小组）涉疫场所暴露人员汇总-737-202207071002'
file_name = "涉疫场所暴露人员汇总-（6）(1)"


def sort_rule(row):
    ret = 20
    address = row[target_col].value
    print(address)
    for zhen in zhens_simple:
        if zhen in address:
            ret = zhens_simple.index(zhen)
    return ret


def main():
    # read data from excel, then sort data by fifth column
    file_path = f"/Users/<USER>/Downloads/{file_name}.xlsx"
    workbook = load_workbook(file_path)
    sheet = workbook.active
    rows = []
    for row in sheet.rows:
        rows.append([cell.value for cell in row if cell])
    # rows = list(sheet.rows)[2:]
    rows.sort(key=lambda x: sort_rule(x))
    for row in rows:
        print(row[4].value)
    write(rows)


def write(all_rows):
    out_file_path = f"/Users/<USER>/Downloads/{file_name}-分组排序.xlsx"
    workbook = Workbook()
    sheet = workbook.active
    sheet.append([])
    sheet.append([])
    for row in all_rows:
        row = [cell.value for cell in row]
        address = row[target_col]
        for zhen in zhens_simple:
            if zhen in address:
                if zhen == "龙兴" and "镇" not in address:
                    continue
                row[write_col] = zhen
                break
        else:
            row[write_col] = "其他"
        sheet.append(row)
    workbook.save(out_file_path)


if __name__ == "__main__":
    main()
