import os
import re

from openpyxl import load_workbook, Workbook


def collect():
    three_times_pattern = re.compile(r'3次')
    three_vaccine_pattern = re.compile(r'þ三针')
    outside_pattern = re.compile(r'þ是')
    non_work_pattern = re.compile(r'学生|^幼儿$|退休|无')
    party_members = []
    students = []
    work_members = []
    out_persons = []
    three_nucleic_acid = []
    three_vaccine = []
    path = '/data/files/sub_item_files'
    all_rows = []
    for root_path, sub_path, files in os.walk(path):
        if not files:
            continue
        for file in files:
            if file.startswith('.'):
                continue
            workbook = load_workbook(os.path.join(root_path, file))
            sheet = workbook.active
            rows = list(sheet.rows)
            family_num = file.split('.')[0].split('_')[0]
            name = rows[2][3].value
            sex = rows[2][6].value
            age = rows[2][8].value
            political_status = rows[2][10].value
            is_family_holder = '是' if outside_pattern.search(rows[2][14].value) else '否'
            id_card = rows[3][3].value
            company = rows[3][9].value
            phone = get_phone(rows[3][12].value)
            address = rows[4][3].value
            members = rows[4][15].value
            is_out = '是' if outside_pattern.search(rows[5][1].value) else '否'
            work_type = get_type(rows[5][12].value)

            all_rows.append((family_num, name, sex, age, political_status,
                             is_family_holder, id_card, company,
                             phone, address, members, is_out, work_type))

    all_rows.sort(key=lambda x: x[0])
    return all_rows


def write(all_rows):
    ret_file = '/data/files/ret.xlsx'
    workbook = Workbook()
    sheet = workbook.active
    sheet.append([])
    for row in all_rows:
        sheet.append(row)
    workbook.save(ret_file)

def get_phone(text):
    pattern = re.compile(r'\d{11}')
    match = pattern.search(text)
    if match:
        return match.group()
    return ''


def get_type(text):
    pattern = re.compile(r'þ(.*?)\s')
    match = pattern.search(text)
    if match:
        return match.group(1)
    return ''

def main():
    all_rows = collect()
    write(all_rows)

if __name__ == '__main__':
    main()