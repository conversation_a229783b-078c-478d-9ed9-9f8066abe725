import collections
import os
import re

from openpyxl import load_workbook
from xlsxtpl.writerx import BookWriter

pattern = re.compile(r"(?P<dst>\d)")

Family = collections.namedtuple(
    "Family",
    [
        "serial",
        "household",
        "members",
        "name",
        "relation",
        "sex",
        "marriage_status",
        "political_status",
        "id_card",
        "education",
        "live_address",
        "residence_address",
        "residence_type",
        "company",
        "is_permanent_residents",
        "phone",
        "special_person",
        "medical_type",
        "pension_type",
        "vaccine_times",
    ],
)


def read_from_excel(path):
    ret = collections.defaultdict(list)
    workbook = load_workbook(path)
    sheet = workbook.active
    rows = list(sheet.rows)
    serial = None
    for row in rows[4:]:
        current_serial = row[0].value
        if current_serial:
            serial = current_serial
        else:
            current_serial = serial
        sex = None
        if row[8].value and len(row[8].value) > 16:
            sex = "男" if int(row[8].value[16]) % 2 == 1 else "女"
        ret[current_serial].append(
            Family(
                serial=current_serial,
                household=row[1].value,
                members=row[2].value,
                name=row[3].value,
                relation=row[4].value,
                sex=sex,
                marriage_status=row[6].value,
                political_status=row[7].value,
                id_card=row[8].value,
                education=row[9].value,
                live_address=row[10].value,
                residence_address=row[11].value,
                residence_type=row[12].value,
                company=row[13].value,
                is_permanent_residents=row[14].value,
                phone=row[15].value,
                special_person=row[16].value,
                medical_type=row[17].value,
                pension_type=row[18].value,
                vaccine_times=row[19].value,
            )
        )

    return ret


def write_from_template(datas):
    path = os.path.dirname(__file__)
    template_path = os.path.join(path, "疫情防控网格化管理入户信息采集表.xlsx")
    for _, items in datas.items():
        for item in items:
            if not item.name:
                continue
            writer = BookWriter(template_path)
            writer.jinja_env.globals.update(dir=dir, getattr=getattr)
            data = {
                "name": item.name,
                "sex": item.sex,
                "political_status": item.political_status,
                "id_card": item.id_card,
                "company": item.company,
                "phone": item.phone,
                "address": item.live_address,
                "family_members": len(items),
            }
            payloads = [data]
            writer.render_book(payloads=payloads)
            result_file_path = os.path.join(
                path, "../../data/files/excel_tool_file/ret_files", f"入户信息采集表_{item.name}.xlsx"
            )
            sheet = writer.workbook.active
            is_household = "☑是  □否" if item.name == item.household else "□是  ☑否"
            vaccine_times = get_vaccine_times(item.vaccine_times)
            is_vaccine = "☑是  □否" if vaccine_times else "□是  ☑否"
            if vaccine_times == 1:
                vaccine_desc = "☑一针  □两针  □三针"
            elif vaccine_times == 2:
                vaccine_desc = "□一针  ☑两针  □三针"
            elif vaccine_times == 3:
                vaccine_desc = "□一针  □两针  ☑三针"
            else:
                vaccine_desc = "□一针  □两针  □三针"
            sheet.cell(row=3, column=15).value = is_household
            sheet.cell(row=6, column=2).value = "是否在外地工作 □是  ☑否"
            sheet.cell(row=9, column=7).value = is_vaccine
            sheet.cell(row=9, column=13).value = vaccine_desc
            writer.save(result_file_path)
            print(f"{item.name} 写入完成")


def get_vaccine_times(vaccine_desc):
    if not vaccine_desc:
        return False
    mather = pattern.search(str(vaccine_desc))
    if not mather:
        return False
    return int(mather.group(1))


def main():
    path = "/data/files/datas.xlsx"
    datas = read_from_excel(path)
    write_from_template(datas)


if __name__ == "__main__":
    main()
