import re
import sys
import os
import gzip


def process_log_content(content):
    error_files = set()
    current_file_id = None
    lines = content.split('\n')
    for line_idx, line in enumerate(lines):
        if not line:
            continue
        # 查找file_id
        file_id_match = re.search(r"create embedding for file_id='(\d+)'.*", line)
        if file_id_match:
            current_file_id = file_id_match.group(1)
            if line_idx + 28 < len(lines):
                error_line = lines[line_idx + 28]
                # 查找PdfInsightNotFound错误
                if 'remarkable.common.exceptions.PdfInsightNotFound:' in error_line:
                    if current_file_id:
                        error_files.add(current_file_id)
                        current_file_id = None  # 重置current_file_id

    return sorted(error_files)


def extract_pdfinsight_errors(log_file):

    try:
        if log_file.endswith('.gz'):
            with gzip.open(log_file, 'rt', encoding='utf-8') as f:
                content = f.read()
        else:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()

        return process_log_content(content)
    except Exception as e:
        print(f"Error processing {log_file}: {e}")
        return set()


def process_directory(directory):
    all_error_files = set()

    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(('.log', '.gz')):
                log_file = os.path.join(root, file)
                error_files = extract_pdfinsight_errors(log_file)
                if error_files:
                    print(f"\nFrom {file}:")
                    for file_id in sorted(error_files):
                        print(f"- {file_id}")
                all_error_files.update(error_files)

    return sorted(all_error_files)


def main():
    path = "/Users/<USER>/workspace/xx/data/logs/Worker Log Development"
    try:
        if os.path.isdir(path):
            all_error_files = process_directory(path)
            if all_error_files:
                print("\nSummary of all PdfInsightNotFound errors:")
                for file_id in all_error_files:
                    print(f"- {file_id}")
            else:
                print("\nNo PdfInsightNotFound errors found in any log files.")
        else:
            error_files = extract_pdfinsight_errors(path)
            if error_files:
                print("\nFound PdfInsightNotFound errors for the following file_ids:")
                for file_id in error_files:
                    print(f"- {file_id}")
            else:
                print("\nNo PdfInsightNotFound errors found.")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
