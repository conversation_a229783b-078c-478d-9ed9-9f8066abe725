import os
import re
import tkinter as tk
from ftplib import FTP
from tkinter import filedialog, messagebox

import pandas as pd

# FTP服务器信息
ftp_server = '***********'  # FTP服务器地址
ftp_username = 'YGDFS002'  # 替换为你的FTP用户名
ftp_password = 'YGDFS002'  # 替换为你的FTP密码

# 固定的站点ID和设备ID
fixed_site_id = '21200'  # 替换为你的固定站点ID
fixed_equipment_id = '2CEE01-LOI'  # 替换为你的固定设备ID

# 桌面路径
desktop_path = os.path.join(os.path.expanduser("~"), 'Desktop')
# 创建lii data文件夹
LOI_data_path = os.path.join(desktop_path, "LOI_data")
os.makedirs(LOI_data_path, exist_ok=True)


def connect_ftp(server, username, password):
    try:
        ftp = FTP()
        ftp.connect(server)
        ftp.login(username, password)
        print("FTP server connect successful~!")
        return ftp
    except Exception as e:
        messagebox.showerror("Connect ftp,connect error", f"connect error FTP server: {e}")
        return None


def change_directory(ftp, dir_path):
    """
    逐步切换到指定的目录
    """
    parts = dir_path.strip('/').split('/')
    for part in parts:
        try:
            ftp.cwd(part)
            print(f"change_directory,change to remote dir successful: {part}")
        except Exception as e:
            print(f"change_directory,try to change remote dir {part} faile: {e}")
            messagebox.showerror("change_directory,dir erroe", f"can not change remote dir: {part}\n error: {e}")
            return False
    return True


def download_files(ftp, remote_path, local_path):
    """
    下载指定远程路径下的所有文件到本地路径。
    """
    if not change_directory(ftp, remote_path):
        print(f"Download file, cannot change to remote dir: {remote_path}")
        return
    try:
        files = ftp.nlst()  # 获取当前目录下的文件列表
        print(f"Download file, acquired remote dir file list: {files}")
        if not files:
            messagebox.showinfo("Download,message", "empty file")
            return
        os.makedirs(local_path, exist_ok=True)  # 创建本地目录
        print(f"Local directory created: {local_path}")
        for file in files:
            file = re.sub(r'[\/\\:*?"<>|]', '_', file)  # 替换非法字符
            local_file = os.path.join(local_path, file)
            print(f"Attempting to download: {file} to {local_file}")
            try:
                with open(local_file, 'wb') as f:
                    ftp.retrbinary(f'RETR {file}', f.write)
                print(f"File downloaded: {local_file}")
            except Exception as e:
                print(f"Failed to download {file}: {e}")
                messagebox.showerror("下载错误", f"Failed to download {file}: {e}")
    except Exception as e:
        messagebox.showerror("下载错误", f"下载文件失败: {e}")


def validate_input(lot_id, glass_id):
    """
    验证输入的格式是否正确。
    """
    lot_id = str(lot_id)  # 确保Lot ID是字符串
    glass_id = str(glass_id)  # 确保Glass ID是字符串

    if len(lot_id) < 9 or len(glass_id) == 0:
        messagebox.showerror("输入错误", "Lot ID 或 Glass ID 格式不正确")
        return False
    return True


def manual_input():
    """
    手动输入Lot ID 和 Glass ID，下载对应的文件。
    """
    print("manual test")
    lot_id = lot_id_entry.get()
    glass_id = glass_id_entry.get()
    if not validate_input(lot_id, glass_id):
        return

    remote_path = f"{fixed_site_id}/{fixed_equipment_id}/{lot_id[:4]}/{lot_id[4:7]}/{lot_id[7:9]}/Source/{glass_id}.IMG"
    local_path = os.path.join(LOI_data_path, f"{fixed_site_id}_{fixed_equipment_id}_{lot_id}_{glass_id}.IMG")
    ftp = connect_ftp(ftp_server, ftp_username, ftp_password)
    if ftp:
        if change_directory(ftp, remote_path):
            download_files(ftp, '', local_path)
        else:
            print(f"can not change to remote dir: {remote_path}")
        ftp.quit()  # 手动下载后断开FTP连接


def import_from_excel():
    """
    从Excel文件导入Lot ID 和 Glass ID，批量下载文件。
    """
    file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xlsx;*.xls")])
    if file_path:
        try:
            df = pd.read_excel(file_path)
            for index, row in df.iterrows():
                lot_id = str(row['LOT_ID']).strip()
                glass_id = str(row['GLASS_ID']).strip()
                if not validate_input(lot_id, glass_id):
                    continue

                remote_path = f"{fixed_site_id}/{fixed_equipment_id}/{lot_id[:4]}/{lot_id[4:7]}/{lot_id[7:9]}/Source/{glass_id}.IMG"
                local_path = os.path.join(
                    LOI_data_path, f"{fixed_site_id}_{fixed_equipment_id}_{lot_id}_{glass_id}.IMG"
                )

                # 重新连接FTP
                ftp = connect_ftp(ftp_server, ftp_username, ftp_password)
                if ftp:
                    if change_directory(ftp, remote_path):
                        download_files(ftp, '', local_path)
                    else:
                        print(f"无法切换到远程目录: {remote_path}")
                    ftp.quit()  # 处理完一行后断开FTP连接
                else:
                    print("FTP连接失败，无法下载文件。")

        except Exception as e:
            messagebox.showerror("错误", f"导入Excel文件时出错: {e}")


# 创建Tkinter界面
root = tk.Tk()
root.title("LOI Data")

# 输入框
tk.Label(root, text="Lot ID :").grid(row=0, column=0, padx=10, pady=10)
lot_id_entry = tk.Entry(root)
lot_id_entry.grid(row=0, column=1, padx=10, pady=10)

tk.Label(root, text="Glass ID :").grid(row=1, column=0, padx=10, pady=10)
glass_id_entry = tk.Entry(root)
glass_id_entry.grid(row=1, column=1, padx=10, pady=10)

# 按钮
manual_button = tk.Button(root, text="手动下载", command=manual_input)
manual_button.grid(row=2, column=0, padx=50, pady=10)

excel_button = tk.Button(root, text="从Excel导入", command=import_from_excel)
excel_button.grid(row=2, column=1, padx=50, pady=10)

# 在这里添加版权信息
copyright_label = tk.Label(root, text="Produced by Wind-Kimi", font=("Helvetica", 8), fg="gray")
copyright_label.grid(row=4, column=0, columnspan=2, sticky="ew", padx=10, pady=5)

root.mainloop()
