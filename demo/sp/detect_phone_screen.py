import cv2
import numpy as np
from ultralytics import <PERSON><PERSON><PERSON>


def detect_phone_screen(image_path):
    # 加载YOLOv8模型
    model = YOLO('yolov8n.pt')

    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError("无法读取图像")

    # 运行检测
    results = model(image)

    # 获取检测结果
    for result in results:
        boxes = result.boxes
        for box in boxes:
            # 如果检测到手机或者显示屏
            if box.cls == 67 or box.cls == 62:  # 67是手机类别，62是显示器类别
                x1, y1, x2, y2 = box.xyxy[0]
                x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)

                # 裁剪屏幕区域
                cropped_screen = image[y1:y2, x1:x2]

                # 保存裁剪后的图像
                cv2.imwrite('cropped_screen.jpg', cropped_screen)

                # 在原图上画框
                cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)

    # 保存标注后的图像
    cv2.imwrite('detected_result.jpg', image)

    return 'cropped_screen.jpg', 'detected_result.jpg'


if __name__ == "__main__":
    # 使用示例
    image_path = "WechatIMG2949.jpg"  # 替换为您的图片路径
    cropped_path, result_path = detect_phone_screen(image_path)
    print(f"裁剪后的图片保存在: {cropped_path}")
    print(f"检测结果保存在: {result_path}")
