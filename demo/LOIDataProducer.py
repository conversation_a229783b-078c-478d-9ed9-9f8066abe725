import os
import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
from ftplib import FTP


# FTP服务器信息
ftp_server = '10.69.2.100'  # FTP服务器地址
ftp_username = 'YGDFS002'  # 替换为你的FTP用户名
ftp_password = 'YGDFS002'  # 替换为你的FTP密码

# 桌面路径
desktop_path = os.path.join(os.path.expanduser("~"), 'Desktop')


def connect_ftp(server, username, password):
    try:
        ftp = FTP()
        ftp.connect(server)
        ftp.login(username, password)
        print("Connect ftp,FTP server connect successful~!")
        return ftp
    except Exception as e:
        messagebox.showerror("Connect ftp,connect error", f"connect error FTP server: {e}")
        return None


def change_directory(ftp, dir_path):
    """
    逐步切换到指定的目录
    """
    parts = dir_path.strip('/').split('/')
    for part in parts:
        try:
            ftp.cwd(part)
            print(f"change_directory,change to remote dir successful: {part}")
        except Exception as e:
            print(f"change_directory,try to change remote dir {part} faile: {e}")
            messagebox.showerror("change_directory,dir erroe", f"can not change remote dir: {part}\n error: {e}")
            return False
    return True


def list_directory(ftp):
    """
    列出当前目录下的所有文件和文件夹
    """
    try:
        files = ftp.nlst()
        print(f"当前目录下的文件和文件夹: {files}")
        return files
    except Exception as e:
        print(f"列出目录失败: {e}")
        return []


def download_files(ftp, remote_path, local_path):
    """
    下载指定远程路径下的所有文件到本地路径。
    """
    if not change_directory(ftp, remote_path):
        print(f"Download file, can not change to remote dir: {remote_path}")
        return

    try:
        files = ftp.nlst()  # 获取当前目录下的文件列表
        print(f"Download file, acquier remote dir file list: {files}")  # 调试信息
        if not files:
            messagebox.showinfo("Download file,tipps", "远程目录为空")
        return

        os.makedirs(local_path, exist_ok=True)  # 创建本地目录

        for file in files:
            local_file = os.path.join(local_path, file)
            with open(local_file, 'wb') as f:
                ftp.retrbinary(f'RETR {file}', f.write)
            print(f"文件已下载: {local_file}")

        messagebox.showinfo("下载完成", "所有文件下载完成")
    except Exception as e:
        messagebox.showerror("下载错误", f"下载文件失败: {e}")
    finally:
        ftp.quit()


def validate_input(site_id, equipment_id, lot_id, glass_id):
    """
    验证输入的格式是否正确。
    """
    if len(site_id) == 0 or len(equipment_id) == 0 or len(lot_id) < 9 or len(glass_id) == 0:
        messagebox.showerror("输入错误", "站点ID、设备ID、Lot ID 或 Glass ID 格式不正确")
        return False
    return True


def manual_input():
    """
    手动输入站点ID、设备ID、Lot ID 和 Glass ID，下载对应的文件。
    """
    print("manual test")
    site_id = site_id_entry.get()
    equipment_id = equipment_id_entry.get()
    lot_id = lot_id_entry.get()
    glass_id = glass_id_entry.get()
    if not validate_input(site_id, equipment_id, lot_id, glass_id):
        return

    # 构造完整的远程路径
    remote_path = f"{site_id}/{equipment_id}/{lot_id[:4]}/{lot_id[4:7]}/{lot_id[7:9]}/Source/{glass_id}"
    local_path = os.path.join(desktop_path, f"{site_id}_{equipment_id}_{lot_id} {glass_id}")
    ftp = connect_ftp(ftp_server, ftp_username, ftp_password)
    if ftp:
        # list_directory(ftp)  # 列出当前目录
        if change_directory(ftp, remote_path):
            download_files(ftp, '', local_path)
        else:
            print(f"can not change to remote dir: {remote_path}")


def import_from_excel():
    """
    从Excel文件导入站点ID、设备ID、Lot ID 和 Glass ID，批量下载文件。
    """
    file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xlsx;*.xls")])
    if file_path:
        try:
            df = pd.read_excel(file_path)
            ftp = connect_ftp(ftp_server, ftp_username, ftp_password)
            if not ftp:
                return  # 如果FTP连接失败，直接退出

            for _, row in df.iterrows():
                site_id = row['Site ID']
                equipment_id = row['Equipment ID']
                lot_id = row['Lot ID']
                glass_id = row['Glass ID']
                if not validate_input(site_id, equipment_id, lot_id, glass_id):
                    continue

                remote_path = f"{site_id}/{equipment_id}/{lot_id[:4]}/{lot_id[4:7]}/{lot_id[7:9]}/source/{glass_id}"
                local_path = os.path.join(desktop_path, f"{site_id}_{equipment_id}_{lot_id} {glass_id}")
                if change_directory(ftp, remote_path):
                    download_files(ftp, '', local_path)
                else:
                    print(f"无法切换到远程目录: {remote_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导入Excel文件时出错: {e}")
        finally:
            if ftp:
                ftp.quit()


# 创建Tkinter界面
root = tk.Tk()
root.title("文件下载工具")

# 输入框
tk.Label(root, text="站点ID (Site ID):").grid(row=0, column=0, padx=10, pady=10)
site_id_entry = tk.Entry(root)
site_id_entry.grid(row=0, column=1, padx=10, pady=10)

tk.Label(root, text="设备ID (Equipment ID):").grid(row=1, column=0, padx=10, pady=10)
equipment_id_entry = tk.Entry(root)
equipment_id_entry.grid(row=1, column=1, padx=10, pady=10)

tk.Label(root, text="Lot ID (格式: XXXXXXXXX):").grid(row=2, column=0, padx=10, pady=10)
lot_id_entry = tk.Entry(root)
lot_id_entry.grid(row=2, column=1, padx=10, pady=10)

tk.Label(root, text="Glass ID (非空):").grid(row=3, column=0, padx=10, pady=10)
glass_id_entry = tk.Entry(root)
glass_id_entry.grid(row=3, column=1, padx=10, pady=10)

# 按钮
manual_button = tk.Button(root, text="手动下载", command=manual_input)
manual_button.grid(row=4, column=0, padx=10, pady=10)

excel_button = tk.Button(root, text="从Excel导入", command=import_from_excel)
excel_button.grid(row=4, column=1, padx=10, pady=10)

root.mainloop()
