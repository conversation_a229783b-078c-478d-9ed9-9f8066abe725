from gmft import CroppedTable, TableDetector, AutoTableFormatter
from gmft.pdf_bindings import PyPDFium2Document

detector = TableDetector()
formatter = AutoTableFormatter()


def ingest_pdf(pdf_path):  # produces list[CroppedTable]
    doc = PyPDFium2Document(pdf_path)
    tables = []
    for page in doc:
        tables += detector.extract(page)
    return tables, doc


tables, doc = ingest_pdf("/Users/<USER>/workspace/xx/data/招商银行_281.pdf")
doc.close()  # once you're done with the document
