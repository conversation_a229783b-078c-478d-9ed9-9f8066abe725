*.DS_Store
*.pyc
*.swp
*.swo
*.ipynb
*.jar
watch.json
dump.rdb
my_python/
*.o303
*.pdb
*.db
data/pg_data
data/files
data/trainning_cache
data/answers
data/results
data/match_table_schema
data/export_answer_data
data/model/match_table_schema
remarkable/match_table_schema
tools/pdf_files/*
tools/pdf_files_bak/*
tools/pdf_files_副本/*
tmp
tools/hkex/*.csv
*.DS_Store

.idea/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
# *.so

# Distribution / packaging
.Python
env/
./build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Translations
*.mo

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule.*

# dotenv
.env

# virtualenv
.venv/
venv/
ENV/

# Spyder project settings
.spyderproject

# Rope project settings
.ropeproject

# vscode
.vs/
.vscode/

# node
node_modules/

#local
data
diff_result.html
*.wav
tools/*.png
tools/pic
/bytedance_code/demo_encrypted.pdf
/tools/hkex/policy-esg-summary-20250224.csv
/tools/hkex/policy-esg-url-20250222.csv
/tools/hkex/Jura 6_IR_Log_Reports_AGM & Poll_20250221_translated.xlsx
/tools/hkex/Jura 6_IR_Log_Reports_AGM & Poll_20250221.xlsx
