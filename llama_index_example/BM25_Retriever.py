import Stemmer
from llama_index.core import Settings, SimpleDirectoryReader
from llama_index.core.node_parser import Sen<PERSON><PERSON><PERSON>plitter
from llama_index.core.response.notebook_utils import display_source_node
from llama_index.core.storage.docstore import SimpleDocumentStore
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.retrievers.bm25 import BM25Retriever

Settings.llm = OpenAI(model="gpt-4o-mini")
Settings.embed_model = OpenAIEmbedding(model_name="text-embedding-3-small")


documents = SimpleDirectoryReader("../data/paul_graham").load_data()

# initialize node parser
splitter = SentenceSplitter(chunk_size=512)

nodes = splitter.get_nodes_from_documents(documents)


# bm25_retriever = BM25Retriever.from_defaults(
#     nodes=nodes,
#     similarity_top_k=2,
#     # Optional: We can pass in the stemmer and set the language for stopwords
#     # This is important for removing stopwords and stemming the query + text
#     # The default is english for both
#     stemmer=Stemmer.Stemmer("english"),
#     language="english",
# )
#
# bm25_retriever.persist("./bm25_retriever")
#
# loaded_bm25_retriever = BM25Retriever.from_persist_dir("./bm25_retriever")

docstore = SimpleDocumentStore()
docstore.add_documents(nodes)

bm25_retriever = BM25Retriever.from_defaults(
    docstore=docstore,
    similarity_top_k=2,
    # Optional: We can pass in the stemmer and set the language for stopwords
    # This is important for removing stopwords and stemming the query + text
    # The default is english for both
    stemmer=Stemmer.Stemmer("english"),
    language="english",
)

retrieved_nodes = bm25_retriever.retrieve("What happened at Viaweb and Interleaf?")
for node in retrieved_nodes:
    display_source_node(node, source_length=5000)
