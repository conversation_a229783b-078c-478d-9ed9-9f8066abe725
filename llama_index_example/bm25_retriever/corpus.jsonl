{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"422b4141-86ad-44ef-b05e-2cfd58d49c2f\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"a548e922-48e6-4260-b6c4-fd2890c48a80\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"409416dd5fb4dfe99dc3faee1c8d5bd29f3d16751f08c0723bf647ad39898421\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"What I Worked On\\n\\nFebruary 2021\\n\\nBefore college the two main things I worked on, outside of school, were writing and programming. I didn't write essays. I wrote what beginning writers were supposed to write then, and probably still are: short stories. My stories were awful. They had hardly any plot, just characters with strong feelings, which I imagined made them deep.\\n\\nThe first programs I tried writing were on the IBM 1401 that our school district used for what was then called \\\"data processing.\\\" This was in 9th grade, so I was 13 or 14. The school district's 1401 happened to be in the basement of our junior high school, and my friend Rich Draves and I got permission to use it. It was like a mini Bond villain's lair down there, with all these alien-looking machines \\u2014 CPU, disk drives, printer, card reader \\u2014 sitting up on a raised floor under bright fluorescent lights.\\n\\nThe language we used was an early version of Fortran. You had to type programs on punch cards, then stack them in the card reader and press a button to load the program into memory and run it. The result would ordinarily be to print something on the spectacularly loud printer.\\n\\nI was puzzled by the 1401. I couldn't figure out what to do with it. And in retrospect there's not much I could have done with it. The only form of input to programs was data stored on punched cards, and I didn't have any data stored on punched cards. The only other option was to do things that didn't rely on any input, like calculate approximations of pi, but I didn't know enough math to do anything interesting of that type. So I'm not surprised I can't remember any programs I wrote, because they can't have done much. My clearest memory is of the moment I learned it was possible for programs not to terminate, when one of mine didn't. On a machine without time-sharing, this was a social as well as a technical error, as the data center manager's expression made clear.\\n\\nWith microcomputers, everything changed.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 2, \"end_char_idx\": 1983, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"a548e922-48e6-4260-b6c4-fd2890c48a80\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"422b4141-86ad-44ef-b05e-2cfd58d49c2f\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"080a933c072d1b962a9b28a017dafa240a497b2c2a53b67743ae99dd66c76164\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"44227af3-a3d5-4b3e-b2c7-c4dec36e79c9\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"c8d417673ba8dadb7be73dc07d0533bf0c3aab4206103c59a7ed3bed7d781312\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I was puzzled by the 1401. I couldn't figure out what to do with it. And in retrospect there's not much I could have done with it. The only form of input to programs was data stored on punched cards, and I didn't have any data stored on punched cards. The only other option was to do things that didn't rely on any input, like calculate approximations of pi, but I didn't know enough math to do anything interesting of that type. So I'm not surprised I can't remember any programs I wrote, because they can't have done much. My clearest memory is of the moment I learned it was possible for programs not to terminate, when one of mine didn't. On a machine without time-sharing, this was a social as well as a technical error, as the data center manager's expression made clear.\\n\\nWith microcomputers, everything changed. Now you could have a computer sitting right in front of you, on a desk, that could respond to your keystrokes as it was running instead of just churning through a stack of punch cards and then stopping. [1]\\n\\nThe first of my friends to get a microcomputer built it himself. It was sold as a kit by Heathkit. I remember vividly how impressed and envious I felt watching him sitting in front of it, typing programs right into the computer.\\n\\nComputers were expensive in those days and it took me years of nagging before I convinced my father to buy one, a TRS-80, in about 1980. The gold standard then was the Apple II, but a TRS-80 was good enough. This was when I really started programming. I wrote simple games, a program to predict how high my model rockets would fly, and a word processor that my father used to write at least one book. There was only room in memory for about 2 pages of text, so he'd write 2 pages at a time and then print them out, but it was a lot better than a typewriter.\\n\\nThough I liked programming, I didn't plan to study it in college. In college I was going to study philosophy, which sounded much more powerful.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 1164, \"end_char_idx\": 3124, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"44227af3-a3d5-4b3e-b2c7-c4dec36e79c9\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"a548e922-48e6-4260-b6c4-fd2890c48a80\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"4b45c5aff978ad2c15cec19b395acdda8e4876699a1875ed7a24fd9173136fe5\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8b6075e1-20c7-436b-be68-d94da01eba7a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"838a87b2ede520fc4ca34c939cc63cf2b2796a9b41c49d850a92cb10fd3aa07a\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I remember vividly how impressed and envious I felt watching him sitting in front of it, typing programs right into the computer.\\n\\nComputers were expensive in those days and it took me years of nagging before I convinced my father to buy one, a TRS-80, in about 1980. The gold standard then was the Apple II, but a TRS-80 was good enough. This was when I really started programming. I wrote simple games, a program to predict how high my model rockets would fly, and a word processor that my father used to write at least one book. There was only room in memory for about 2 pages of text, so he'd write 2 pages at a time and then print them out, but it was a lot better than a typewriter.\\n\\nThough I liked programming, I didn't plan to study it in college. In college I was going to study philosophy, which sounded much more powerful. It seemed, to my naive high school self, to be the study of the ultimate truths, compared to which the things studied in other fields would be mere domain knowledge. What I discovered when I got to college was that the other fields took up so much of the space of ideas that there wasn't much left for these supposed ultimate truths. All that seemed left for philosophy were edge cases that people in other fields felt could safely be ignored.\\n\\nI couldn't have put this into words when I was 18. All I knew at the time was that I kept taking philosophy courses and they kept being boring. So I decided to switch to AI.\\n\\nAI was in the air in the mid 1980s, but there were two things especially that made me want to work on it: a novel by Heinlein called The Moon is a Harsh Mistress, which featured an intelligent computer called Mike, and a PBS documentary that showed Terry Winograd using SHRDLU. I haven't tried rereading The Moon is a Harsh Mistress, so I don't know how well it has aged, but when I read it I was drawn entirely into its world. It seemed only a matter of time before we'd have Mike, and when I saw Winograd using SHRDLU, it seemed like that time would be a few years at most.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 2291, \"end_char_idx\": 4320, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"8b6075e1-20c7-436b-be68-d94da01eba7a\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"44227af3-a3d5-4b3e-b2c7-c4dec36e79c9\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"8b35ce200eff8c820ff9dd553e6aa8ec11089be83bf63660dadddfc00b90bb36\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"aab27318-bfca-4eb6-8888-b569f2255cd3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"ffbb9e6bb03d3ec68b4bf25c39ad5e8d38fc7fd2f90a33152947922872346682\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I couldn't have put this into words when I was 18. All I knew at the time was that I kept taking philosophy courses and they kept being boring. So I decided to switch to AI.\\n\\nAI was in the air in the mid 1980s, but there were two things especially that made me want to work on it: a novel by Heinlein called The Moon is a Harsh Mistress, which featured an intelligent computer called Mike, and a PBS documentary that showed Terry Winograd using SHRDLU. I haven't tried rereading The Moon is a Harsh Mistress, so I don't know how well it has aged, but when I read it I was drawn entirely into its world. It seemed only a matter of time before we'd have Mike, and when I saw Winograd using SHRDLU, it seemed like that time would be a few years at most. All you had to do was teach SHRDLU more words.\\n\\nThere weren't any classes in AI at Cornell then, not even graduate classes, so I started trying to teach myself. Which meant learning Lisp, since in those days Lisp was regarded as the language of AI. The commonly used programming languages then were pretty primitive, and programmers' ideas correspondingly so. The default language at Cornell was a Pascal-like language called PL\/I, and the situation was similar elsewhere. Learning Lisp expanded my concept of a program so fast that it was years before I started to have a sense of where the new limits were. This was more like it; this was what I had expected college to do. It wasn't happening in a class, like it was supposed to, but that was ok. For the next couple years I was on a roll. I knew what I was going to do.\\n\\nFor my undergraduate thesis, I reverse-engineered SHRDLU. My God did I love working on that program. It was a pleasing bit of code, but what made it even more exciting was my belief \\u2014 hard to imagine now, but not unique in 1985 \\u2014 that it was already climbing the lower slopes of intelligence.\\n\\nI had gotten into a program at Cornell that didn't make you choose a major. You could take whatever classes you liked, and choose whatever you liked to put on your degree.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 3570, \"end_char_idx\": 5611, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"aab27318-bfca-4eb6-8888-b569f2255cd3\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8b6075e1-20c7-436b-be68-d94da01eba7a\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"3219e836a7ec085de87a2a4145baced8154602141cd4afdde056842dbef5c3f9\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"1d447b67-a10e-4d84-ba81-4f1917aa3369\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5447c6d173f62130a7827451e50954578341ca5165095947ab0bac1364f76a59\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Learning Lisp expanded my concept of a program so fast that it was years before I started to have a sense of where the new limits were. This was more like it; this was what I had expected college to do. It wasn't happening in a class, like it was supposed to, but that was ok. For the next couple years I was on a roll. I knew what I was going to do.\\n\\nFor my undergraduate thesis, I reverse-engineered SHRDLU. My God did I love working on that program. It was a pleasing bit of code, but what made it even more exciting was my belief \\u2014 hard to imagine now, but not unique in 1985 \\u2014 that it was already climbing the lower slopes of intelligence.\\n\\nI had gotten into a program at Cornell that didn't make you choose a major. You could take whatever classes you liked, and choose whatever you liked to put on your degree. I of course chose \\\"Artificial Intelligence.\\\" When I got the actual physical diploma, I was dismayed to find that the quotes had been included, which made them read as scare-quotes. At the time this bothered me, but now it seems amusingly accurate, for reasons I was about to discover.\\n\\nI applied to 3 grad schools: MIT and Yale, which were renowned for AI at the time, and Harvard, which I'd visited because Rich Draves went there, and was also home to Bill Woods, who'd invented the type of parser I used in my SHRDLU clone. Only Harvard accepted me, so that was where I went.\\n\\nI don't remember the moment it happened, or if there even was a specific moment, but during the first year of grad school I realized that AI, as practiced at the time, was a hoax. By which I mean the sort of AI in which a program that's told \\\"the dog is sitting on the chair\\\" translates this into some formal representation and adds it to the list of things it knows.\\n\\nWhat these programs really showed was that there's a subset of natural language that's a formal language. But a very proper subset. It was clear that there was an unbridgeable gap between what they could do and actually understanding natural language. It was not, in fact, simply a matter of teaching SHRDLU more words.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 4794, \"end_char_idx\": 6879, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"1d447b67-a10e-4d84-ba81-4f1917aa3369\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"aab27318-bfca-4eb6-8888-b569f2255cd3\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"6b48776ad364105d97473eefde4af1043cfed848cfd281ecaed4db36d7009254\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"858abb79-7308-4e24-bf5a-ce3e2871c4c6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"835855cdbe431b751fff47c73fcc36c4d54afe37ef7566d97d9cafee531b0ed0\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Only Harvard accepted me, so that was where I went.\\n\\nI don't remember the moment it happened, or if there even was a specific moment, but during the first year of grad school I realized that AI, as practiced at the time, was a hoax. By which I mean the sort of AI in which a program that's told \\\"the dog is sitting on the chair\\\" translates this into some formal representation and adds it to the list of things it knows.\\n\\nWhat these programs really showed was that there's a subset of natural language that's a formal language. But a very proper subset. It was clear that there was an unbridgeable gap between what they could do and actually understanding natural language. It was not, in fact, simply a matter of teaching SHRDLU more words. That whole way of doing AI, with explicit data structures representing concepts, was not going to work. Its brokenness did, as so often happens, generate a lot of opportunities to write papers about various band-aids that could be applied to it, but it was never going to get us Mike.\\n\\nSo I looked around to see what I could salvage from the wreckage of my plans, and there was Lisp. I knew from experience that Lisp was interesting for its own sake and not just for its association with AI, even though that was the main reason people cared about it at the time. So I decided to focus on Lisp. In fact, I decided to write a book about Lisp hacking. It's scary to think how little I knew about Lisp hacking when I started writing that book. But there's nothing like writing a book about something to help you learn it. The book, On Lisp, wasn't published till 1993, but I wrote much of it in grad school.\\n\\nComputer Science is an uneasy alliance between two halves, theory and systems. The theory people prove things, and the systems people build things. I wanted to build things. I had plenty of respect for theory \\u2014 indeed, a sneaking suspicion that it was the more admirable of the two halves \\u2014 but building things seemed so much more exciting.\\n\\nThe problem with systems work, though, was that it didn't last. Any program you wrote today, no matter how good, would be obsolete in a couple decades at best.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 6138, \"end_char_idx\": 8287, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"858abb79-7308-4e24-bf5a-ce3e2871c4c6\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"1d447b67-a10e-4d84-ba81-4f1917aa3369\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"b4e280e5e6452f2bddddbd12a9ed4d089bd0b21b6817de9e7942c7717048dfc1\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"5b54c72a-41ef-4d9b-9dbd-7278810c3c59\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"6ab22ad0d726b5a07969158b083f3a9b7ad8afa08ca57a5ae1b2e3444a456a0a\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"So I decided to focus on Lisp. In fact, I decided to write a book about Lisp hacking. It's scary to think how little I knew about Lisp hacking when I started writing that book. But there's nothing like writing a book about something to help you learn it. The book, On Lisp, wasn't published till 1993, but I wrote much of it in grad school.\\n\\nComputer Science is an uneasy alliance between two halves, theory and systems. The theory people prove things, and the systems people build things. I wanted to build things. I had plenty of respect for theory \\u2014 indeed, a sneaking suspicion that it was the more admirable of the two halves \\u2014 but building things seemed so much more exciting.\\n\\nThe problem with systems work, though, was that it didn't last. Any program you wrote today, no matter how good, would be obsolete in a couple decades at best. People might mention your software in footnotes, but no one would actually use it. And indeed, it would seem very feeble work. Only people with a sense of the history of the field would even realize that, in its time, it had been good.\\n\\nThere were some surplus Xerox Dandelions floating around the computer lab at one point. Anyone who wanted one to play around with could have one. I was briefly tempted, but they were so slow by present standards; what was the point? No one else wanted one either, so off they went. That was what happened to systems work.\\n\\nI wanted not just to build things, but to build things that would last.\\n\\nIn this dissatisfied state I went in 1988 to visit Rich Draves at CMU, where he was in grad school. One day I went to visit the Carnegie Institute, where I'd spent a lot of time as a kid. While looking at a painting there I realized something that might seem obvious, but was a big surprise to me. There, right on the wall, was something you could make that would last. Paintings didn't become obsolete. Some of the best ones were hundreds of years old.\\n\\nAnd moreover this was something you could make a living doing.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 7444, \"end_char_idx\": 9438, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"5b54c72a-41ef-4d9b-9dbd-7278810c3c59\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"858abb79-7308-4e24-bf5a-ce3e2871c4c6\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"8b4335b1f79ee14abd29e1d951df805a249f3b9125864a5da6014e2beab9aeb4\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"223fd174-cc29-41ef-9b75-d5377c968943\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"131ecf7823379dedd4b1747b1e6681c8400856e051df964aa447ed9b5ed3f1a1\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Anyone who wanted one to play around with could have one. I was briefly tempted, but they were so slow by present standards; what was the point? No one else wanted one either, so off they went. That was what happened to systems work.\\n\\nI wanted not just to build things, but to build things that would last.\\n\\nIn this dissatisfied state I went in 1988 to visit Rich Draves at CMU, where he was in grad school. One day I went to visit the Carnegie Institute, where I'd spent a lot of time as a kid. While looking at a painting there I realized something that might seem obvious, but was a big surprise to me. There, right on the wall, was something you could make that would last. Paintings didn't become obsolete. Some of the best ones were hundreds of years old.\\n\\nAnd moreover this was something you could make a living doing. Not as easily as you could by writing software, of course, but I thought if you were really industrious and lived really cheaply, it had to be possible to make enough to survive. And as an artist you could be truly independent. You wouldn't have a boss, or even need to get research funding.\\n\\nI had always liked looking at paintings. Could I make them? I had no idea. I'd never imagined it was even possible. I knew intellectually that people made art \\u2014 that it didn't just appear spontaneously \\u2014 but it was as if the people who made it were a different species. They either lived long ago or were mysterious geniuses doing strange things in profiles in Life magazine. The idea of actually being able to make art, to put that verb before that noun, seemed almost miraculous.\\n\\nThat fall I started taking art classes at Harvard. Grad students could take classes in any department, and my advisor, Tom Cheatham, was very easy going. If he even knew about the strange classes I was taking, he never said anything.\\n\\nSo now I was in a PhD program in computer science, yet planning to be an artist, yet also genuinely in love with Lisp hacking and working away at On Lisp. In other words, like many a grad student, I was working energetically on multiple projects that were not my thesis.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 8613, \"end_char_idx\": 10720, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"223fd174-cc29-41ef-9b75-d5377c968943\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"5b54c72a-41ef-4d9b-9dbd-7278810c3c59\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"b5f25ae5c727e3fe96ada1858cc1e3381cbecca37a4b789c6806c6b05a418402\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"ee09d4db-1848-4f0d-b396-bc4df2f3a3cf\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"8c9a6b379b8522ad841ff078f606de56bf9319a52d19e312de633e13a0db05f4\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I'd never imagined it was even possible. I knew intellectually that people made art \\u2014 that it didn't just appear spontaneously \\u2014 but it was as if the people who made it were a different species. They either lived long ago or were mysterious geniuses doing strange things in profiles in Life magazine. The idea of actually being able to make art, to put that verb before that noun, seemed almost miraculous.\\n\\nThat fall I started taking art classes at Harvard. Grad students could take classes in any department, and my advisor, Tom Cheatham, was very easy going. If he even knew about the strange classes I was taking, he never said anything.\\n\\nSo now I was in a PhD program in computer science, yet planning to be an artist, yet also genuinely in love with Lisp hacking and working away at On Lisp. In other words, like many a grad student, I was working energetically on multiple projects that were not my thesis.\\n\\nI didn't see a way out of this situation. I didn't want to drop out of grad school, but how else was I going to get out? I remember when my friend Robert Morris got kicked out of Cornell for writing the internet worm of 1988, I was envious that he'd found such a spectacular way to get out of grad school.\\n\\nThen one day in April 1990 a crack appeared in the wall. I ran into professor Cheatham and he asked if I was far enough along to graduate that June. I didn't have a word of my dissertation written, but in what must have been the quickest bit of thinking in my life, I decided to take a shot at writing one in the 5 weeks or so that remained before the deadline, reusing parts of On Lisp where I could, and I was able to respond, with no perceptible delay \\\"Yes, I think so. I'll give you something to read in a few days.\\\"\\n\\nI picked applications of continuations as the topic. In retrospect I should have written about macros and embedded languages. There's a whole world there that's barely been explored. But all I wanted was to get out of grad school, and my rapidly written dissertation sufficed, just barely.\\n\\nMeanwhile I was applying to art schools.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 9807, \"end_char_idx\": 11882, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"ee09d4db-1848-4f0d-b396-bc4df2f3a3cf\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"223fd174-cc29-41ef-9b75-d5377c968943\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"3bb1ca2397c92e2db27165097ca4fa614135e50dd3677646d3387c1aa6b3ce7a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"572715ff-0734-4457-8ef1-85984d6b595f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"58808e5955d0c65b54b2872b8913d5196f3b6b037032e001f6d00720c7fac748\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Then one day in April 1990 a crack appeared in the wall. I ran into professor Cheatham and he asked if I was far enough along to graduate that June. I didn't have a word of my dissertation written, but in what must have been the quickest bit of thinking in my life, I decided to take a shot at writing one in the 5 weeks or so that remained before the deadline, reusing parts of On Lisp where I could, and I was able to respond, with no perceptible delay \\\"Yes, I think so. I'll give you something to read in a few days.\\\"\\n\\nI picked applications of continuations as the topic. In retrospect I should have written about macros and embedded languages. There's a whole world there that's barely been explored. But all I wanted was to get out of grad school, and my rapidly written dissertation sufficed, just barely.\\n\\nMeanwhile I was applying to art schools. I applied to two: RISD in the US, and the Accademia di Belli Arti in Florence, which, because it was the oldest art school, I imagined would be good. RISD accepted me, and I never heard back from the Accademia, so off to Providence I went.\\n\\nI'd applied for the BFA program at RISD, which meant in effect that I had to go to college again. This was not as strange as it sounds, because I was only 25, and art schools are full of people of different ages. RISD counted me as a transfer sophomore and said I had to do the foundation that summer. The foundation means the classes that everyone has to take in fundamental subjects like drawing, color, and design.\\n\\nToward the end of the summer I got a big surprise: a letter from the Accademia, which had been delayed because they'd sent it to Cambridge England instead of Cambridge Massachusetts, inviting me to take the entrance exam in Florence that fall. This was now only weeks away. My nice landlady let me leave my stuff in her attic. I had some money saved from consulting work I'd done in grad school; there was probably enough to last a year if I lived cheaply. Now all I had to do was learn Italian.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 11029, \"end_char_idx\": 13038, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"572715ff-0734-4457-8ef1-85984d6b595f\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"ee09d4db-1848-4f0d-b396-bc4df2f3a3cf\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"e38618c43adafe3294cc9db095ff8c64896418954ffbd294bf3beef7e35148c5\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d137dd42-442c-4f67-8524-d79f8394b118\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"0606a13a21d706809b19867b5c4f672019d8bd58304b8e29d2056891e03e3c30\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"This was not as strange as it sounds, because I was only 25, and art schools are full of people of different ages. RISD counted me as a transfer sophomore and said I had to do the foundation that summer. The foundation means the classes that everyone has to take in fundamental subjects like drawing, color, and design.\\n\\nToward the end of the summer I got a big surprise: a letter from the Accademia, which had been delayed because they'd sent it to Cambridge England instead of Cambridge Massachusetts, inviting me to take the entrance exam in Florence that fall. This was now only weeks away. My nice landlady let me leave my stuff in her attic. I had some money saved from consulting work I'd done in grad school; there was probably enough to last a year if I lived cheaply. Now all I had to do was learn Italian.\\n\\nOnly stranieri (foreigners) had to take this entrance exam. In retrospect it may well have been a way of excluding them, because there were so many stranieri attracted by the idea of studying art in Florence that the Italian students would otherwise have been outnumbered. I was in decent shape at painting and drawing from the RISD foundation that summer, but I still don't know how I managed to pass the written exam. I remember that I answered the essay question by writing about Cezanne, and that I cranked up the intellectual level as high as I could to make the most of my limited vocabulary. [2]\\n\\nI'm only up to age 25 and already there are such conspicuous patterns. Here I was, yet again about to attend some august institution in the hopes of learning about some prestigious subject, and yet again about to be disappointed. The students and faculty in the painting department at the Accademia were the nicest people you could imagine, but they had long since arrived at an arrangement whereby the students wouldn't require the faculty to teach anything, and in return the faculty wouldn't require the students to learn anything. And at the same time all involved would adhere outwardly to the conventions of a 19th century atelier.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 12222, \"end_char_idx\": 14281, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"d137dd42-442c-4f67-8524-d79f8394b118\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"572715ff-0734-4457-8ef1-85984d6b595f\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"245cd2995af1ee974749269c8ac82b4dc2018cf6c4c51269004ab680f733fe20\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"3ac1d3b8-a1c4-4b28-b682-d1984e698716\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"1284f233b61837f60dc3ff6fa4c516555642c83cb50fc764bc2c55341528a1c2\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I remember that I answered the essay question by writing about Cezanne, and that I cranked up the intellectual level as high as I could to make the most of my limited vocabulary. [2]\\n\\nI'm only up to age 25 and already there are such conspicuous patterns. Here I was, yet again about to attend some august institution in the hopes of learning about some prestigious subject, and yet again about to be disappointed. The students and faculty in the painting department at the Accademia were the nicest people you could imagine, but they had long since arrived at an arrangement whereby the students wouldn't require the faculty to teach anything, and in return the faculty wouldn't require the students to learn anything. And at the same time all involved would adhere outwardly to the conventions of a 19th century atelier. We actually had one of those little stoves, fed with kindling, that you see in 19th century studio paintings, and a nude model sitting as close to it as possible without getting burned. Except hardly anyone else painted her besides me. The rest of the students spent their time chatting or occasionally trying to imitate things they'd seen in American art magazines.\\n\\nOur model turned out to live just down the street from me. She made a living from a combination of modelling and making fakes for a local antique dealer. She'd copy an obscure old painting out of a book, and then he'd take the copy and maltreat it to make it look old. [3]\\n\\nWhile I was a student at the Accademia I started painting still lives in my bedroom at night. These paintings were tiny, because the room was, and because I painted them on leftover scraps of canvas, which was all I could afford at the time. Painting still lives is different from painting people, because the subject, as its name suggests, can't move. People can't sit for more than about 15 minutes at a time, and when they do they don't sit very still. So the traditional m.o. for painting people is to know how to paint a generic person, which you then modify to match the specific person you're painting. Whereas a still life you can, if you want, copy pixel by pixel from what you're seeing.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 13460, \"end_char_idx\": 15621, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"3ac1d3b8-a1c4-4b28-b682-d1984e698716\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d137dd42-442c-4f67-8524-d79f8394b118\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"328b7353967c22f5b142581cfc8e61665b8b65be0c4db6edde873fd8b6485911\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"ef41139d-acb4-4f3f-8f66-e751b94cb0ed\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"711c1eb5f68ffdf29fc6bc66b8d545e93c5c2cc93426303cfbb3a0cf54dcbd5a\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"She'd copy an obscure old painting out of a book, and then he'd take the copy and maltreat it to make it look old. [3]\\n\\nWhile I was a student at the Accademia I started painting still lives in my bedroom at night. These paintings were tiny, because the room was, and because I painted them on leftover scraps of canvas, which was all I could afford at the time. Painting still lives is different from painting people, because the subject, as its name suggests, can't move. People can't sit for more than about 15 minutes at a time, and when they do they don't sit very still. So the traditional m.o. for painting people is to know how to paint a generic person, which you then modify to match the specific person you're painting. Whereas a still life you can, if you want, copy pixel by pixel from what you're seeing. You don't want to stop there, of course, or you get merely photographic accuracy, and what makes a still life interesting is that it's been through a head. You want to emphasize the visual cues that tell you, for example, that the reason the color changes suddenly at a certain point is that it's the edge of an object. By subtly emphasizing such things you can make paintings that are more realistic than photographs not just in some metaphorical sense, but in the strict information-theoretic sense. [4]\\n\\nI liked painting still lives because I was curious about what I was seeing. In everyday life, we aren't consciously aware of much we're seeing. Most visual perception is handled by low-level processes that merely tell your brain \\\"that's a water droplet\\\" without telling you details like where the lightest and darkest points are, or \\\"that's a bush\\\" without telling you the shape and position of every leaf. This is a feature of brains, not a bug. In everyday life it would be distracting to notice every leaf on every bush. But when you have to paint something, you have to look more closely, and when you do there's a lot to see.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 14804, \"end_char_idx\": 16759, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"ef41139d-acb4-4f3f-8f66-e751b94cb0ed\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"3ac1d3b8-a1c4-4b28-b682-d1984e698716\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"e792934be3ad13a8e9a1dd84161b5c12578e41e78c24dae321c4523dbe02b49c\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"6b4bde40-bf5e-4e80-ab7d-556ed8631d3c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f807b9e8f6df829ffda86bc552ff8caad63a31de236ee72e269b20ad235b2720\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"By subtly emphasizing such things you can make paintings that are more realistic than photographs not just in some metaphorical sense, but in the strict information-theoretic sense. [4]\\n\\nI liked painting still lives because I was curious about what I was seeing. In everyday life, we aren't consciously aware of much we're seeing. Most visual perception is handled by low-level processes that merely tell your brain \\\"that's a water droplet\\\" without telling you details like where the lightest and darkest points are, or \\\"that's a bush\\\" without telling you the shape and position of every leaf. This is a feature of brains, not a bug. In everyday life it would be distracting to notice every leaf on every bush. But when you have to paint something, you have to look more closely, and when you do there's a lot to see. You can still be noticing new things after days of trying to paint something people usually take for granted, just as you can after days of trying to write an essay about something people usually take for granted.\\n\\nThis is not the only way to paint. I'm not 100% sure it's even a good way to paint. But it seemed a good enough bet to be worth trying.\\n\\nOur teacher, professor Ulivi, was a nice guy. He could see I worked hard, and gave me a good grade, which he wrote down in a sort of passport each student had. But the Accademia wasn't teaching me anything except Italian, and my money was running out, so at the end of the first year I went back to the US.\\n\\nI wanted to go back to RISD, but I was now broke and RISD was very expensive, so I decided to get a job for a year and then return to RISD the next fall. I got one at a company called Interleaf, which made software for creating documents. You mean like Microsoft Word? Exactly. That was how I learned that low end software tends to eat high end software. But Interleaf still had a few years to live yet. [5]\\n\\nInterleaf had done something pretty bold. Inspired by Emacs, they'd added a scripting language, and even made the scripting language a dialect of Lisp.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 15942, \"end_char_idx\": 17980, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"6b4bde40-bf5e-4e80-ab7d-556ed8631d3c\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"ef41139d-acb4-4f3f-8f66-e751b94cb0ed\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"078b44f164743f7ccc1c60e59ab4f49e1b63c3c3e89aadc4cbdb905af33cb95a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"ee5587de-b8c0-43a3-9f10-a0b917d2e3f1\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"438790f35588c902c7aece6d9b2924ab42e7009ed3ac203d770544628977ef3b\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"But the Accademia wasn't teaching me anything except Italian, and my money was running out, so at the end of the first year I went back to the US.\\n\\nI wanted to go back to RISD, but I was now broke and RISD was very expensive, so I decided to get a job for a year and then return to RISD the next fall. I got one at a company called Interleaf, which made software for creating documents. You mean like Microsoft Word? Exactly. That was how I learned that low end software tends to eat high end software. But Interleaf still had a few years to live yet. [5]\\n\\nInterleaf had done something pretty bold. Inspired by Emacs, they'd added a scripting language, and even made the scripting language a dialect of Lisp. Now they wanted a Lisp hacker to write things in it. This was the closest thing I've had to a normal job, and I hereby apologize to my boss and coworkers, because I was a bad employee. Their Lisp was the thinnest icing on a giant C cake, and since I didn't know C and didn't want to learn it, I never understood most of the software. Plus I was terribly irresponsible. This was back when a programming job meant showing up every day during certain working hours. That seemed unnatural to me, and on this point the rest of the world is coming around to my way of thinking, but at the time it caused a lot of friction. Toward the end of the year I spent much of my time surreptitiously working on On Lisp, which I had by this time gotten a contract to publish.\\n\\nThe good part was that I got paid huge amounts of money, especially by art student standards. In Florence, after paying my part of the rent, my budget for everything else had been $7 a day. Now I was getting paid more than 4 times that every hour, even when I was just sitting in a meeting. By living cheaply I not only managed to save enough to go back to RISD, but also paid off my college loans.\\n\\nI learned some useful things at Interleaf, though they were mostly about what not to do.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 17272, \"end_char_idx\": 19229, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"ee5587de-b8c0-43a3-9f10-a0b917d2e3f1\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"6b4bde40-bf5e-4e80-ab7d-556ed8631d3c\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"59f22905ba67475c5afbd71ad90d83861803360cc20a9db703ce78b9bc27b0aa\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"61c33054-7498-46be-93c3-9c1c2991b42c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d8a489781dcc1864c2c21ee63335985a7f194186ad3ad3fa47122b8ca5efb360\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"That seemed unnatural to me, and on this point the rest of the world is coming around to my way of thinking, but at the time it caused a lot of friction. Toward the end of the year I spent much of my time surreptitiously working on On Lisp, which I had by this time gotten a contract to publish.\\n\\nThe good part was that I got paid huge amounts of money, especially by art student standards. In Florence, after paying my part of the rent, my budget for everything else had been $7 a day. Now I was getting paid more than 4 times that every hour, even when I was just sitting in a meeting. By living cheaply I not only managed to save enough to go back to RISD, but also paid off my college loans.\\n\\nI learned some useful things at Interleaf, though they were mostly about what not to do. I learned that it's better for technology companies to be run by product people than sales people (though sales is a real skill and people who are good at it are really good at it), that it leads to bugs when code is edited by too many people, that cheap office space is no bargain if it's depressing, that planned meetings are inferior to corridor conversations, that big, bureaucratic customers are a dangerous source of money, and that there's not much overlap between conventional office hours and the optimal time for hacking, or conventional offices and the optimal place for it.\\n\\nBut the most important thing I learned, and which I used in both Viaweb and Y Combinator, is that the low end eats the high end: that it's good to be the \\\"entry level\\\" option, even though that will be less prestigious, because if you're not, someone else will be, and will squash you against the ceiling. Which in turn means that prestige is a danger sign.\\n\\nWhen I left to go back to RISD the next fall, I arranged to do freelance work for the group that did projects for customers, and this was how I survived for the next several years. When I came back to visit for a project later on, someone told me about a new thing called HTML, which was, as he described it, a derivative of SGML.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 18444, \"end_char_idx\": 20505, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"61c33054-7498-46be-93c3-9c1c2991b42c\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"ee5587de-b8c0-43a3-9f10-a0b917d2e3f1\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"de63eea6f2c814268f25cd01bffe7f12eef42fafdc942f576e89c6b515d1edb0\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d298d775-1f85-42c6-9182-0df7d2fc73fb\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5c42d2aaeb03cf50770f147f3f9f335de8dc0446e3e025e205f4d61e1c3e9840\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"But the most important thing I learned, and which I used in both Viaweb and Y Combinator, is that the low end eats the high end: that it's good to be the \\\"entry level\\\" option, even though that will be less prestigious, because if you're not, someone else will be, and will squash you against the ceiling. Which in turn means that prestige is a danger sign.\\n\\nWhen I left to go back to RISD the next fall, I arranged to do freelance work for the group that did projects for customers, and this was how I survived for the next several years. When I came back to visit for a project later on, someone told me about a new thing called HTML, which was, as he described it, a derivative of SGML. Markup language enthusiasts were an occupational hazard at Interleaf and I ignored him, but this HTML thing later became a big part of my life.\\n\\nIn the fall of 1992 I moved back to Providence to continue at RISD. The foundation had merely been intro stuff, and the Accademia had been a (very civilized) joke. Now I was going to see what real art school was like. But alas it was more like the Accademia than not. Better organized, certainly, and a lot more expensive, but it was now becoming clear that art school did not bear the same relationship to art that medical school bore to medicine. At least not the painting department. The textile department, which my next door neighbor belonged to, seemed to be pretty rigorous. No doubt illustration and architecture were too. But painting was post-rigorous. Painting students were supposed to express themselves, which to the more worldly ones meant to try to cook up some sort of distinctive signature style.\\n\\nA signature style is the visual equivalent of what in show business is known as a \\\"schtick\\\": something that immediately identifies the work as yours and no one else's. For example, when you see a painting that looks like a certain kind of cartoon, you know it's by Roy Lichtenstein. So if you see a big painting of this type hanging in the apartment of a hedge fund manager, you know he paid millions of dollars for it.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 19817, \"end_char_idx\": 21886, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"d298d775-1f85-42c6-9182-0df7d2fc73fb\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"61c33054-7498-46be-93c3-9c1c2991b42c\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"36197ef7d73aebeaa1730e8af7739f2efbe4b5f45f10cc68e094dbdac0f38a1a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"9f5de93c-9d4a-4822-86bb-6dec2dbf621d\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"860eda3dce612493d1cb4677fae3676b5fa7c7214917c4f2f419b115916ce381\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"At least not the painting department. The textile department, which my next door neighbor belonged to, seemed to be pretty rigorous. No doubt illustration and architecture were too. But painting was post-rigorous. Painting students were supposed to express themselves, which to the more worldly ones meant to try to cook up some sort of distinctive signature style.\\n\\nA signature style is the visual equivalent of what in show business is known as a \\\"schtick\\\": something that immediately identifies the work as yours and no one else's. For example, when you see a painting that looks like a certain kind of cartoon, you know it's by Roy Lichtenstein. So if you see a big painting of this type hanging in the apartment of a hedge fund manager, you know he paid millions of dollars for it. That's not always why artists have a signature style, but it's usually why buyers pay a lot for such work. [6]\\n\\nThere were plenty of earnest students too: kids who \\\"could draw\\\" in high school, and now had come to what was supposed to be the best art school in the country, to learn to draw even better. They tended to be confused and demoralized by what they found at RISD, but they kept going, because painting was what they did. I was not one of the kids who could draw in high school, but at RISD I was definitely closer to their tribe than the tribe of signature style seekers.\\n\\nI learned a lot in the color class I took at RISD, but otherwise I was basically teaching myself to paint, and I could do that for free. So in 1993 I dropped out. I hung around Providence for a bit, and then my college friend Nancy Parmet did me a big favor. A rent-controlled apartment in a building her mother owned in New York was becoming vacant. Did I want it? It wasn't much more than my current place, and New York was supposed to be where the artists were. So yes, I wanted it! [7]\\n\\nAsterix comics begin by zooming in on a tiny corner of Roman Gaul that turns out not to be controlled by the Romans.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 21100, \"end_char_idx\": 23077, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"9f5de93c-9d4a-4822-86bb-6dec2dbf621d\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d298d775-1f85-42c6-9182-0df7d2fc73fb\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"4eb16ddc54fea1ef27efe5483a44d005644b71b770ba4eda1a44657950a087d0\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8ea2975a-7e7b-4713-9b6b-32a31f904b6d\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b74720e4175216beec221d551511d139cf83879735b03b8c7327037d177a2f41\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I was not one of the kids who could draw in high school, but at RISD I was definitely closer to their tribe than the tribe of signature style seekers.\\n\\nI learned a lot in the color class I took at RISD, but otherwise I was basically teaching myself to paint, and I could do that for free. So in 1993 I dropped out. I hung around Providence for a bit, and then my college friend Nancy Parmet did me a big favor. A rent-controlled apartment in a building her mother owned in New York was becoming vacant. Did I want it? It wasn't much more than my current place, and New York was supposed to be where the artists were. So yes, I wanted it! [7]\\n\\nAsterix comics begin by zooming in on a tiny corner of Roman Gaul that turns out not to be controlled by the Romans. You can do something similar on a map of New York City: if you zoom in on the Upper East Side, there's a tiny corner that's not rich, or at least wasn't in 1993. It's called Yorkville, and that was my new home. Now I was a New York artist \\u2014 in the strictly technical sense of making paintings and living in New York.\\n\\nI was nervous about money, because I could sense that Interleaf was on the way down. Freelance Lisp hacking work was very rare, and I didn't want to have to program in another language, which in those days would have meant C++ if I was lucky. So with my unerring nose for financial opportunity, I decided to write another book on Lisp. This would be a popular book, the sort of book that could be used as a textbook. I imagined myself living frugally off the royalties and spending all my time painting. (The painting on the cover of this book, ANSI Common Lisp, is one that I painted around this time.)\\n\\nThe best thing about New York for me was the presence of Idelle and Julian Weber. Idelle Weber was a painter, one of the early photorealists, and I'd taken her painting class at Harvard. I've never known a teacher more beloved by her students. Large numbers of former students kept in touch with her, including me.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 22318, \"end_char_idx\": 24315, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"8ea2975a-7e7b-4713-9b6b-32a31f904b6d\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"9f5de93c-9d4a-4822-86bb-6dec2dbf621d\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"751bde7b27ba70c60f2fd112d8d19316846e33bc3bfc318eaada5136ca81b8eb\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d6454a7e-b875-4ab1-b99e-5521cf659d8f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a4a7b726d45d7efced34191c766f85392b634f88cc046c442907c6c725f0ea7c\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Freelance Lisp hacking work was very rare, and I didn't want to have to program in another language, which in those days would have meant C++ if I was lucky. So with my unerring nose for financial opportunity, I decided to write another book on Lisp. This would be a popular book, the sort of book that could be used as a textbook. I imagined myself living frugally off the royalties and spending all my time painting. (The painting on the cover of this book, ANSI Common Lisp, is one that I painted around this time.)\\n\\nThe best thing about New York for me was the presence of Idelle and Julian Weber. Idelle Weber was a painter, one of the early photorealists, and I'd taken her painting class at Harvard. I've never known a teacher more beloved by her students. Large numbers of former students kept in touch with her, including me. After I moved to New York I became her de facto studio assistant.\\n\\nShe liked to paint on big, square canvases, 4 to 5 feet on a side. One day in late 1994 as I was stretching one of these monsters there was something on the radio about a famous fund manager. He wasn't that much older than me, and was super rich. The thought suddenly occurred to me: why don't I become rich? Then I'll be able to work on whatever I want.\\n\\nMeanwhile I'd been hearing more and more about this new thing called the World Wide Web. Robert Morris showed it to me when I visited him in Cambridge, where he was now in grad school at Harvard. It seemed to me that the web would be a big deal. I'd seen what graphical user interfaces had done for the popularity of microcomputers. It seemed like the web would do the same for the internet.\\n\\nIf I wanted to get rich, here was the next train leaving the station. I was right about that part. What I got wrong was the idea. I decided we should start a company to put art galleries online. I can't honestly say, after reading so many Y Combinator applications, that this was the worst startup idea ever, but it was up there.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 23481, \"end_char_idx\": 25461, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"d6454a7e-b875-4ab1-b99e-5521cf659d8f\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8ea2975a-7e7b-4713-9b6b-32a31f904b6d\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"357c8e495790676adac9a6bbbe314656265e08eb51a1ea36090b795308b6e9dc\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"7dca00fb-66f0-4bb2-bb43-6951db9addce\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"e45b00971863335f71a090fc8d32bcb4cd22a39ca7295fa491fe0ee5300f76b9\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"The thought suddenly occurred to me: why don't I become rich? Then I'll be able to work on whatever I want.\\n\\nMeanwhile I'd been hearing more and more about this new thing called the World Wide Web. Robert Morris showed it to me when I visited him in Cambridge, where he was now in grad school at Harvard. It seemed to me that the web would be a big deal. I'd seen what graphical user interfaces had done for the popularity of microcomputers. It seemed like the web would do the same for the internet.\\n\\nIf I wanted to get rich, here was the next train leaving the station. I was right about that part. What I got wrong was the idea. I decided we should start a company to put art galleries online. I can't honestly say, after reading so many Y Combinator applications, that this was the worst startup idea ever, but it was up there. Art galleries didn't want to be online, and still don't, not the fancy ones. That's not how they sell. I wrote some software to generate web sites for galleries, and Robert wrote some to resize images and set up an http server to serve the pages. Then we tried to sign up galleries. To call this a difficult sale would be an understatement. It was difficult to give away. A few galleries let us make sites for them for free, but none paid us.\\n\\nThen some online stores started to appear, and I realized that except for the order buttons they were identical to the sites we'd been generating for galleries. This impressive-sounding thing called an \\\"internet storefront\\\" was something we already knew how to build.\\n\\nSo in the summer of 1995, after I submitted the camera-ready copy of ANSI Common Lisp to the publishers, we started trying to write software to build online stores. At first this was going to be normal desktop software, which in those days meant Windows software. That was an alarming prospect, because neither of us knew how to write Windows software or wanted to learn. We lived in the Unix world. But we decided we'd at least try writing a prototype store builder on Unix. Robert wrote a shopping cart, and I wrote a new site generator for stores \\u2014 in Lisp, of course.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 24630, \"end_char_idx\": 26746, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"7dca00fb-66f0-4bb2-bb43-6951db9addce\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d6454a7e-b875-4ab1-b99e-5521cf659d8f\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"58d2b420a8889c014fc4b63167f1c2a6d8c202646ae7e92ad2b73185a670f779\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"b1e77ca2-d027-42e3-b7ce-8d4e7d12b6f2\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"08769c79704760d0d1f5fc341ebe0809b3399bcf2beef1d258d825c74f128105\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"A few galleries let us make sites for them for free, but none paid us.\\n\\nThen some online stores started to appear, and I realized that except for the order buttons they were identical to the sites we'd been generating for galleries. This impressive-sounding thing called an \\\"internet storefront\\\" was something we already knew how to build.\\n\\nSo in the summer of 1995, after I submitted the camera-ready copy of ANSI Common Lisp to the publishers, we started trying to write software to build online stores. At first this was going to be normal desktop software, which in those days meant Windows software. That was an alarming prospect, because neither of us knew how to write Windows software or wanted to learn. We lived in the Unix world. But we decided we'd at least try writing a prototype store builder on Unix. Robert wrote a shopping cart, and I wrote a new site generator for stores \\u2014 in Lisp, of course.\\n\\nWe were working out of Robert's apartment in Cambridge. His roommate was away for big chunks of time, during which I got to sleep in his room. For some reason there was no bed frame or sheets, just a mattress on the floor. One morning as I was lying on this mattress I had an idea that made me sit up like a capital L. What if we ran the software on the server, and let users control it by clicking on links? Then we'd never have to write anything to run on users' computers. We could generate the sites on the same server we'd serve them from. Users wouldn't need anything more than a browser.\\n\\nThis kind of software, known as a web app, is common now, but at the time it wasn't clear that it was even possible. To find out, we decided to try making a version of our store builder that you could control through the browser. A couple days later, on August 12, we had one that worked. The UI was horrible, but it proved you could build a whole store through the browser, without any client software or typing anything into the command line on the server.\\n\\nNow we felt like we were really onto something. I had visions of a whole new generation of software working this way. You wouldn't need versions, or ports, or any of that crap.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 25834, \"end_char_idx\": 27980, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"b1e77ca2-d027-42e3-b7ce-8d4e7d12b6f2\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"7dca00fb-66f0-4bb2-bb43-6951db9addce\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"cd06b0601d11714fe6c6ed433a3938a3f46136d1a0eadcfd771f16a05bf4a013\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8e2ec03f-5194-44d6-bbc1-19c793503f50\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d007a8e01b5f156f103bde5c3df8b5025cb24357fae1c049a94167a4082a1fe8\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Then we'd never have to write anything to run on users' computers. We could generate the sites on the same server we'd serve them from. Users wouldn't need anything more than a browser.\\n\\nThis kind of software, known as a web app, is common now, but at the time it wasn't clear that it was even possible. To find out, we decided to try making a version of our store builder that you could control through the browser. A couple days later, on August 12, we had one that worked. The UI was horrible, but it proved you could build a whole store through the browser, without any client software or typing anything into the command line on the server.\\n\\nNow we felt like we were really onto something. I had visions of a whole new generation of software working this way. You wouldn't need versions, or ports, or any of that crap. At Interleaf there had been a whole group called Release Engineering that seemed to be at least as big as the group that actually wrote the software. Now you could just update the software right on the server.\\n\\nWe started a new company we called Viaweb, after the fact that our software worked via the web, and we got $10,000 in seed funding from Idelle's husband Julian. In return for that and doing the initial legal work and giving us business advice, we gave him 10% of the company. Ten years later this deal became the model for Y Combinator's. We knew founders needed something like this, because we'd needed it ourselves.\\n\\nAt this stage I had a negative net worth, because the thousand dollars or so I had in the bank was more than counterbalanced by what I owed the government in taxes. (Had I diligently set aside the proper proportion of the money I'd made consulting for Interleaf? No, I had not.) So although Robert had his graduate student stipend, I needed that seed funding to live on.\\n\\nWe originally hoped to launch in September, but we got more ambitious about the software as we worked on it.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 27157, \"end_char_idx\": 29091, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"8e2ec03f-5194-44d6-bbc1-19c793503f50\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"b1e77ca2-d027-42e3-b7ce-8d4e7d12b6f2\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"0201458ee9675ce7fc05cc715c6ff84a33f1f4c1e198be5ea6ba4a0ce07d0b46\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"001121d4-836f-45e6-873b-dd5dd06a15fc\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"3746ac98c2a1e07dafeb299950f8fc0392f1aaa923bf4a502dcb5d3af0af24af\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"In return for that and doing the initial legal work and giving us business advice, we gave him 10% of the company. Ten years later this deal became the model for Y Combinator's. We knew founders needed something like this, because we'd needed it ourselves.\\n\\nAt this stage I had a negative net worth, because the thousand dollars or so I had in the bank was more than counterbalanced by what I owed the government in taxes. (Had I diligently set aside the proper proportion of the money I'd made consulting for Interleaf? No, I had not.) So although Robert had his graduate student stipend, I needed that seed funding to live on.\\n\\nWe originally hoped to launch in September, but we got more ambitious about the software as we worked on it. Eventually we managed to build a WYSIWYG site builder, in the sense that as you were creating pages, they looked exactly like the static ones that would be generated later, except that instead of leading to static pages, the links all referred to closures stored in a hash table on the server.\\n\\nIt helped to have studied art, because the main goal of an online store builder is to make users look legit, and the key to looking legit is high production values. If you get page layouts and fonts and colors right, you can make a guy running a store out of his bedroom look more legit than a big company.\\n\\n(If you're curious why my site looks so old-fashioned, it's because it's still made with this software. It may look clunky today, but in 1996 it was the last word in slick.)\\n\\nIn September, Robert rebelled. \\\"We've been working on this for a month,\\\" he said, \\\"and it's still not done.\\\" This is funny in retrospect, because he would still be working on it almost 3 years later. But I decided it might be prudent to recruit more programmers, and I asked Robert who else in grad school with him was really good. He recommended Trevor Blackwell, which surprised me at first, because at that point I knew Trevor mainly for his plan to reduce everything in his life to a stack of notecards, which he carried around with him. But Rtm was right, as usual. Trevor turned out to be a frighteningly effective hacker.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 28353, \"end_char_idx\": 30498, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"001121d4-836f-45e6-873b-dd5dd06a15fc\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8e2ec03f-5194-44d6-bbc1-19c793503f50\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"6206a64528836370ecfe6c1d44c7cd0bb7487ecb39b71cfdaec8825459d5d1c1\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"5f70e4ce-5037-485a-9500-20cb225caee6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"7470a51debffef8e6ca7ab7a51055c796509951bd144a62fb5f3bf443aadaaf5\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"(If you're curious why my site looks so old-fashioned, it's because it's still made with this software. It may look clunky today, but in 1996 it was the last word in slick.)\\n\\nIn September, Robert rebelled. \\\"We've been working on this for a month,\\\" he said, \\\"and it's still not done.\\\" This is funny in retrospect, because he would still be working on it almost 3 years later. But I decided it might be prudent to recruit more programmers, and I asked Robert who else in grad school with him was really good. He recommended Trevor Blackwell, which surprised me at first, because at that point I knew Trevor mainly for his plan to reduce everything in his life to a stack of notecards, which he carried around with him. But Rtm was right, as usual. Trevor turned out to be a frighteningly effective hacker.\\n\\nIt was a lot of fun working with Robert and Trevor. They're the two most independent-minded people I know, and in completely different ways. If you could see inside Rtm's brain it would look like a colonial New England church, and if you could see inside Trevor's it would look like the worst excesses of Austrian Rococo.\\n\\nWe opened for business, with 6 stores, in January 1996. It was just as well we waited a few months, because although we worried we were late, we were actually almost fatally early. There was a lot of talk in the press then about ecommerce, but not many people actually wanted online stores. [8]\\n\\nThere were three main parts to the software: the editor, which people used to build sites and which I wrote, the shopping cart, which Robert wrote, and the manager, which kept track of orders and statistics, and which Trevor wrote. In its time, the editor was one of the best general-purpose site builders. I kept the code tight and didn't have to integrate with any other software except Robert's and Trevor's, so it was quite fun to work on. If all I'd had to do was work on this software, the next 3 years would have been the easiest of my life.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 29695, \"end_char_idx\": 31667, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"5f70e4ce-5037-485a-9500-20cb225caee6\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"001121d4-836f-45e6-873b-dd5dd06a15fc\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"b6663f00d156752e0f3a6f3550f424ae6b5323771edf62c47490de382b98804a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"e716b70a-2ac1-4b33-867c-d5855b528c3f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"baf67275a2b8e3a660debf80260b1f008e62b60b5818ad129e8dadd5b4396d4b\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"We opened for business, with 6 stores, in January 1996. It was just as well we waited a few months, because although we worried we were late, we were actually almost fatally early. There was a lot of talk in the press then about ecommerce, but not many people actually wanted online stores. [8]\\n\\nThere were three main parts to the software: the editor, which people used to build sites and which I wrote, the shopping cart, which Robert wrote, and the manager, which kept track of orders and statistics, and which Trevor wrote. In its time, the editor was one of the best general-purpose site builders. I kept the code tight and didn't have to integrate with any other software except Robert's and Trevor's, so it was quite fun to work on. If all I'd had to do was work on this software, the next 3 years would have been the easiest of my life. Unfortunately I had to do a lot more, all of it stuff I was worse at than programming, and the next 3 years were instead the most stressful.\\n\\nThere were a lot of startups making ecommerce software in the second half of the 90s. We were determined to be the Microsoft Word, not the Interleaf. Which meant being easy to use and inexpensive. It was lucky for us that we were poor, because that caused us to make Viaweb even more inexpensive than we realized. We charged $100 a month for a small store and $300 a month for a big one. This low price was a big attraction, and a constant thorn in the sides of competitors, but it wasn't because of some clever insight that we set the price low. We had no idea what businesses paid for things. $300 a month seemed like a lot of money to us.\\n\\nWe did a lot of things right by accident like that. For example, we did what's now called \\\"doing things that don't scale,\\\" although at the time we would have described it as \\\"being so lame that we're driven to the most desperate measures to get users.\\\" The most common of which was building stores for them. This seemed particularly humiliating, since the whole raison d'etre of our software was that people could use it to make their own stores.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 30823, \"end_char_idx\": 32899, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"e716b70a-2ac1-4b33-867c-d5855b528c3f\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"5f70e4ce-5037-485a-9500-20cb225caee6\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"6df368cd9d6f573650cc209b4ba8de0340ce620e37b8ba082ff63a299daf03c4\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"6601c8ef-76eb-44ad-b45a-dd1037e33f70\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2377c42b597548939c8ae12efe917cc4134f03a389ed463f0e40a60a0b229f35\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"We charged $100 a month for a small store and $300 a month for a big one. This low price was a big attraction, and a constant thorn in the sides of competitors, but it wasn't because of some clever insight that we set the price low. We had no idea what businesses paid for things. $300 a month seemed like a lot of money to us.\\n\\nWe did a lot of things right by accident like that. For example, we did what's now called \\\"doing things that don't scale,\\\" although at the time we would have described it as \\\"being so lame that we're driven to the most desperate measures to get users.\\\" The most common of which was building stores for them. This seemed particularly humiliating, since the whole raison d'etre of our software was that people could use it to make their own stores. But anything to get users.\\n\\nWe learned a lot more about retail than we wanted to know. For example, that if you could only have a small image of a man's shirt (and all images were small then by present standards), it was better to have a closeup of the collar than a picture of the whole shirt. The reason I remember learning this was that it meant I had to rescan about 30 images of men's shirts. My first set of scans were so beautiful too.\\n\\nThough this felt wrong, it was exactly the right thing to be doing. Building stores for users taught us about retail, and about how it felt to use our software. I was initially both mystified and repelled by \\\"business\\\" and thought we needed a \\\"business person\\\" to be in charge of it, but once we started to get users, I was converted, in much the same way I was converted to fatherhood once I had kids. Whatever users wanted, I was all theirs. Maybe one day we'd have so many users that I couldn't scan their images for them, but in the meantime there was nothing more important to do.\\n\\nAnother thing I didn't get at the time is that growth rate is the ultimate test of a startup. Our growth rate was fine. We had about 70 stores at the end of 1996 and about 500 at the end of 1997.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 32124, \"end_char_idx\": 34126, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"6601c8ef-76eb-44ad-b45a-dd1037e33f70\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"e716b70a-2ac1-4b33-867c-d5855b528c3f\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"57d8be8b74b05a25754ae2c6167967e2aa9c93a522d1d65f9428f2d8d670d554\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"fdba4d41-0fd5-49ae-87da-653b726cd276\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d765355c7259ed3be4c7b99c75eb0172dc470d0282435a263d04da99bbb1868d\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"My first set of scans were so beautiful too.\\n\\nThough this felt wrong, it was exactly the right thing to be doing. Building stores for users taught us about retail, and about how it felt to use our software. I was initially both mystified and repelled by \\\"business\\\" and thought we needed a \\\"business person\\\" to be in charge of it, but once we started to get users, I was converted, in much the same way I was converted to fatherhood once I had kids. Whatever users wanted, I was all theirs. Maybe one day we'd have so many users that I couldn't scan their images for them, but in the meantime there was nothing more important to do.\\n\\nAnother thing I didn't get at the time is that growth rate is the ultimate test of a startup. Our growth rate was fine. We had about 70 stores at the end of 1996 and about 500 at the end of 1997. I mistakenly thought the thing that mattered was the absolute number of users. And that is the thing that matters in the sense that that's how much money you're making, and if you're not making enough, you might go out of business. But in the long term the growth rate takes care of the absolute number. If we'd been a startup I was advising at Y Combinator, I would have said: Stop being so stressed out, because you're doing fine. You're growing 7x a year. Just don't hire too many more people and you'll soon be profitable, and then you'll control your own destiny.\\n\\nAlas I hired lots more people, partly because our investors wanted me to, and partly because that's what startups did during the Internet Bubble. A company with just a handful of employees would have seemed amateurish. So we didn't reach breakeven until about when Yahoo bought us in the summer of 1998. Which in turn meant we were at the mercy of investors for the entire life of the company. And since both we and our investors were noobs at startups, the result was a mess even by startup standards.\\n\\nIt was a huge relief when Yahoo bought us. In principle our Viaweb stock was valuable. It was a share in a business that was profitable and growing rapidly.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 33298, \"end_char_idx\": 35357, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"fdba4d41-0fd5-49ae-87da-653b726cd276\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"6601c8ef-76eb-44ad-b45a-dd1037e33f70\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"7d13949b158f8d4d31381d3a7a7bd7b1eb62831f554eda927a32f2668a9ff8ba\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"ccc1f993-234f-4f99-9930-7f6046a72dfa\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2bb3e5fc9a07b5e774abf0e6763df69097510f0173dd7bfddd72e832f56c640f\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"You're growing 7x a year. Just don't hire too many more people and you'll soon be profitable, and then you'll control your own destiny.\\n\\nAlas I hired lots more people, partly because our investors wanted me to, and partly because that's what startups did during the Internet Bubble. A company with just a handful of employees would have seemed amateurish. So we didn't reach breakeven until about when Yahoo bought us in the summer of 1998. Which in turn meant we were at the mercy of investors for the entire life of the company. And since both we and our investors were noobs at startups, the result was a mess even by startup standards.\\n\\nIt was a huge relief when Yahoo bought us. In principle our Viaweb stock was valuable. It was a share in a business that was profitable and growing rapidly. But it didn't feel very valuable to me; I had no idea how to value a business, but I was all too keenly aware of the near-death experiences we seemed to have every few months. Nor had I changed my grad student lifestyle significantly since we started. So when Yahoo bought us it felt like going from rags to riches. Since we were going to California, I bought a car, a yellow 1998 VW GTI. I remember thinking that its leather seats alone were by far the most luxurious thing I owned.\\n\\nThe next year, from the summer of 1998 to the summer of 1999, must have been the least productive of my life. I didn't realize it at the time, but I was worn out from the effort and stress of running Viaweb. For a while after I got to California I tried to continue my usual m.o. of programming till 3 in the morning, but fatigue combined with Yahoo's prematurely aged culture and grim cube farm in Santa Clara gradually dragged me down. After a few months it felt disconcertingly like working at Interleaf.\\n\\nYahoo had given us a lot of options when they bought us. At the time I thought Yahoo was so overvalued that they'd never be worth anything, but to my astonishment the stock went up 5x in the next year.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 34560, \"end_char_idx\": 36553, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"ccc1f993-234f-4f99-9930-7f6046a72dfa\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"fdba4d41-0fd5-49ae-87da-653b726cd276\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"76e231cf5b19fb378ba4b483b45733f379da4af112e33a18286ab9e946a4e9db\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"0306e671-43e9-464e-a7b0-2656966bfaae\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"0ca728da54a2d32a88fcd1b32249f1156e7ba37dcde57f2251d4ea47cef71937\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I remember thinking that its leather seats alone were by far the most luxurious thing I owned.\\n\\nThe next year, from the summer of 1998 to the summer of 1999, must have been the least productive of my life. I didn't realize it at the time, but I was worn out from the effort and stress of running Viaweb. For a while after I got to California I tried to continue my usual m.o. of programming till 3 in the morning, but fatigue combined with Yahoo's prematurely aged culture and grim cube farm in Santa Clara gradually dragged me down. After a few months it felt disconcertingly like working at Interleaf.\\n\\nYahoo had given us a lot of options when they bought us. At the time I thought Yahoo was so overvalued that they'd never be worth anything, but to my astonishment the stock went up 5x in the next year. I hung on till the first chunk of options vested, then in the summer of 1999 I left. It had been so long since I'd painted anything that I'd half forgotten why I was doing this. My brain had been entirely full of software and men's shirts for 4 years. But I had done this to get rich so I could paint, I reminded myself, and now I was rich, so I should go paint.\\n\\nWhen I said I was leaving, my boss at Yahoo had a long conversation with me about my plans. I told him all about the kinds of pictures I wanted to paint. At the time I was touched that he took such an interest in me. Now I realize it was because he thought I was lying. My options at that point were worth about $2 million a month. If I was leaving that kind of money on the table, it could only be to go and start some new startup, and if I did, I might take people with me. This was the height of the Internet Bubble, and Yahoo was ground zero of it. My boss was at that moment a billionaire. Leaving then to start a new startup must have seemed to him an insanely, and yet also plausibly, ambitious plan.\\n\\nBut I really was quitting to paint, and I started immediately. There was no time to lose.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 35747, \"end_char_idx\": 37716, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"0306e671-43e9-464e-a7b0-2656966bfaae\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"ccc1f993-234f-4f99-9930-7f6046a72dfa\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"5f3bf76480abe25cf10ce79498bb2e81eab61040a640aebb444db86b600c215d\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"bcf2d37b-6dd6-4d71-962a-872426ac217b\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2ca78f1ee42eb4bb1021e4db1d8760d8305a50db2836a78f0242c22ffdfa7702\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"When I said I was leaving, my boss at Yahoo had a long conversation with me about my plans. I told him all about the kinds of pictures I wanted to paint. At the time I was touched that he took such an interest in me. Now I realize it was because he thought I was lying. My options at that point were worth about $2 million a month. If I was leaving that kind of money on the table, it could only be to go and start some new startup, and if I did, I might take people with me. This was the height of the Internet Bubble, and Yahoo was ground zero of it. My boss was at that moment a billionaire. Leaving then to start a new startup must have seemed to him an insanely, and yet also plausibly, ambitious plan.\\n\\nBut I really was quitting to paint, and I started immediately. There was no time to lose. I'd already burned 4 years getting rich. Now when I talk to founders who are leaving after selling their companies, my advice is always the same: take a vacation. That's what I should have done, just gone off somewhere and done nothing for a month or two, but the idea never occurred to me.\\n\\nSo I tried to paint, but I just didn't seem to have any energy or ambition. Part of the problem was that I didn't know many people in California. I'd compounded this problem by buying a house up in the Santa Cruz Mountains, with a beautiful view but miles from anywhere. I stuck it out for a few more months, then in desperation I went back to New York, where unless you understand about rent control you'll be surprised to hear I still had my apartment, sealed up like a tomb of my old life. Idelle was in New York at least, and there were other people trying to paint there, even though I didn't know any of them.\\n\\nWhen I got back to New York I resumed my old life, except now I was rich. It was as weird as it sounds. I resumed all my old patterns, except now there were doors where there hadn't been.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 36918, \"end_char_idx\": 38813, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"bcf2d37b-6dd6-4d71-962a-872426ac217b\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"0306e671-43e9-464e-a7b0-2656966bfaae\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"7399434cd94d22f0088bf8038264257ba6cc7d0db63c09f12e81df4d6145e7ca\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"3c155fef-557c-4bf9-8ec6-454c19024ed6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"47f1d4853ef874402867b3108ac6256060ddb41e7b7cf4a6de27278d16ce799e\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"So I tried to paint, but I just didn't seem to have any energy or ambition. Part of the problem was that I didn't know many people in California. I'd compounded this problem by buying a house up in the Santa Cruz Mountains, with a beautiful view but miles from anywhere. I stuck it out for a few more months, then in desperation I went back to New York, where unless you understand about rent control you'll be surprised to hear I still had my apartment, sealed up like a tomb of my old life. Idelle was in New York at least, and there were other people trying to paint there, even though I didn't know any of them.\\n\\nWhen I got back to New York I resumed my old life, except now I was rich. It was as weird as it sounds. I resumed all my old patterns, except now there were doors where there hadn't been. Now when I was tired of walking, all I had to do was raise my hand, and (unless it was raining) a taxi would stop to pick me up. Now when I walked past charming little restaurants I could go in and order lunch. It was exciting for a while. Painting started to go better. I experimented with a new kind of still life where I'd paint one painting in the old way, then photograph it and print it, blown up, on canvas, and then use that as the underpainting for a second still life, painted from the same objects (which hopefully hadn't rotted yet).\\n\\nMeanwhile I looked for an apartment to buy. Now I could actually choose what neighborhood to live in. Where, I asked myself and various real estate agents, is the Cambridge of New York? Aided by occasional visits to actual Cambridge, I gradually realized there wasn't one. Huh.\\n\\nAround this time, in the spring of 2000, I had an idea. It was clear from our experience with Viaweb that web apps were the future. Why not build a web app for making web apps? Why not let people edit code on our server through the browser, and then host the resulting applications for them?\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 38009, \"end_char_idx\": 39931, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"3c155fef-557c-4bf9-8ec6-454c19024ed6\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"bcf2d37b-6dd6-4d71-962a-872426ac217b\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"bbdfb5f77df4c7d5baf1dbff4147a762a8a9408a2526aedb626ce2916e5d2c32\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d7872263-73e0-44fd-9c96-b7d14ea45635\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2feb7c4c6f5ed81cbf673c6fbe3e51f4f1980223c0ae0908d46daf8da61ac227\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I experimented with a new kind of still life where I'd paint one painting in the old way, then photograph it and print it, blown up, on canvas, and then use that as the underpainting for a second still life, painted from the same objects (which hopefully hadn't rotted yet).\\n\\nMeanwhile I looked for an apartment to buy. Now I could actually choose what neighborhood to live in. Where, I asked myself and various real estate agents, is the Cambridge of New York? Aided by occasional visits to actual Cambridge, I gradually realized there wasn't one. Huh.\\n\\nAround this time, in the spring of 2000, I had an idea. It was clear from our experience with Viaweb that web apps were the future. Why not build a web app for making web apps? Why not let people edit code on our server through the browser, and then host the resulting applications for them? [9] You could run all sorts of services on the servers that these applications could use just by making an API call: making and receiving phone calls, manipulating images, taking credit card payments, etc.\\n\\nI got so excited about this idea that I couldn't think about anything else. It seemed obvious that this was the future. I didn't particularly want to start another company, but it was clear that this idea would have to be embodied as one, so I decided to move to Cambridge and start it. I hoped to lure Robert into working on it with me, but there I ran into a hitch. Robert was now a postdoc at MIT, and though he'd made a lot of money the last time I'd lured him into working on one of my schemes, it had also been a huge time sink. So while he agreed that it sounded like a plausible idea, he firmly refused to work on it.\\n\\nHmph. Well, I'd do it myself then. I recruited Dan Giffin, who had worked for Viaweb, and two undergrads who wanted summer jobs, and we got to work trying to build what it's now clear is about twenty companies and several open source projects worth of software. The language for defining applications would of course be a dialect of Lisp.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 39085, \"end_char_idx\": 41104, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"d7872263-73e0-44fd-9c96-b7d14ea45635\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"3c155fef-557c-4bf9-8ec6-454c19024ed6\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"5eb080bdb830561287818926cdefd5649d7c50bdaeaccfcf5478cff87c0b87c8\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"0313f2af-7737-4a97-bd9e-1f90aab86fc4\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f55a82cd3aa00352375cadaed7d5b646be1f1655613b8fd3ae4d890c950ef5b8\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I hoped to lure Robert into working on it with me, but there I ran into a hitch. Robert was now a postdoc at MIT, and though he'd made a lot of money the last time I'd lured him into working on one of my schemes, it had also been a huge time sink. So while he agreed that it sounded like a plausible idea, he firmly refused to work on it.\\n\\nHmph. Well, I'd do it myself then. I recruited Dan Giffin, who had worked for Viaweb, and two undergrads who wanted summer jobs, and we got to work trying to build what it's now clear is about twenty companies and several open source projects worth of software. The language for defining applications would of course be a dialect of Lisp. But I wasn't so naive as to assume I could spring an overt Lisp on a general audience; we'd hide the parentheses, like Dylan did.\\n\\nBy then there was a name for the kind of company Viaweb was, an \\\"application service provider,\\\" or ASP. This name didn't last long before it was replaced by \\\"software as a service,\\\" but it was current for long enough that I named this new company after it: it was going to be called Aspra.\\n\\nI started working on the application builder, Dan worked on network infrastructure, and the two undergrads worked on the first two services (images and phone calls). But about halfway through the summer I realized I really didn't want to run a company \\u2014 especially not a big one, which it was looking like this would have to be. I'd only started Viaweb because I needed the money. Now that I didn't need money anymore, why was I doing this? If this vision had to be realized as a company, then screw the vision. I'd build a subset that could be done as an open source project.\\n\\nMuch to my surprise, the time I spent working on this stuff was not wasted after all. After we started Y Combinator, I would often encounter startups working on parts of this new architecture, and it was very useful to have spent so much time thinking about it and even trying to write some of it.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 40426, \"end_char_idx\": 42402, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"0313f2af-7737-4a97-bd9e-1f90aab86fc4\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d7872263-73e0-44fd-9c96-b7d14ea45635\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"b7a1659ef2029c1017ae7d72152e318250d7aa811b55117f139c97be61e3a228\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"e7e9f37c-d9ff-4be3-bafb-26a29ddf48a5\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f1fc5e8b8f5a9632367994fa57e357422337947415c715b202b32d6cb00e0d5a\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I started working on the application builder, Dan worked on network infrastructure, and the two undergrads worked on the first two services (images and phone calls). But about halfway through the summer I realized I really didn't want to run a company \\u2014 especially not a big one, which it was looking like this would have to be. I'd only started Viaweb because I needed the money. Now that I didn't need money anymore, why was I doing this? If this vision had to be realized as a company, then screw the vision. I'd build a subset that could be done as an open source project.\\n\\nMuch to my surprise, the time I spent working on this stuff was not wasted after all. After we started Y Combinator, I would often encounter startups working on parts of this new architecture, and it was very useful to have spent so much time thinking about it and even trying to write some of it.\\n\\nThe subset I would build as an open source project was the new Lisp, whose parentheses I now wouldn't even have to hide. A lot of Lisp hackers dream of building a new Lisp, partly because one of the distinctive features of the language is that it has dialects, and partly, I think, because we have in our minds a Platonic form of Lisp that all existing dialects fall short of. I certainly did. So at the end of the summer Dan and I switched to working on this new dialect of Lisp, which I called Arc, in a house I bought in Cambridge.\\n\\nThe following spring, lightning struck. I was invited to give a talk at a Lisp conference, so I gave one about how we'd used Lisp at Viaweb. Afterward I put a postscript file of this talk online, on paulgraham.com, which I'd created years before using Viaweb but had never used for anything. In one day it got 30,000 page views. What on earth had happened? The referring urls showed that someone had posted it on Slashdot. [10]\\n\\nWow, I thought, there's an audience. If I write something and put it on the web, anyone can read it. That may seem obvious now, but it was surprising then.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 41527, \"end_char_idx\": 43524, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"e7e9f37c-d9ff-4be3-bafb-26a29ddf48a5\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"0313f2af-7737-4a97-bd9e-1f90aab86fc4\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"b8297b56fddc881a3265c235a13dd77829eef78f3a1013cbe80dbb87447e70ab\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"23662e81-944d-4d42-9c4f-4d130e1449e0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"cc53a5b7de6c2e48c28928f9f93c4e7e824665b542a2337f85dd5f67981eab45\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I certainly did. So at the end of the summer Dan and I switched to working on this new dialect of Lisp, which I called Arc, in a house I bought in Cambridge.\\n\\nThe following spring, lightning struck. I was invited to give a talk at a Lisp conference, so I gave one about how we'd used Lisp at Viaweb. Afterward I put a postscript file of this talk online, on paulgraham.com, which I'd created years before using Viaweb but had never used for anything. In one day it got 30,000 page views. What on earth had happened? The referring urls showed that someone had posted it on Slashdot. [10]\\n\\nWow, I thought, there's an audience. If I write something and put it on the web, anyone can read it. That may seem obvious now, but it was surprising then. In the print era there was a narrow channel to readers, guarded by fierce monsters known as editors. The only way to get an audience for anything you wrote was to get it published as a book, or in a newspaper or magazine. Now anyone could publish anything.\\n\\nThis had been possible in principle since 1993, but not many people had realized it yet. I had been intimately involved with building the infrastructure of the web for most of that time, and a writer as well, and it had taken me 8 years to realize it. Even then it took me several years to understand the implications. It meant there would be a whole new generation of essays. [11]\\n\\nIn the print era, the channel for publishing essays had been vanishingly small. Except for a few officially anointed thinkers who went to the right parties in New York, the only people allowed to publish essays were specialists writing about their specialties. There were so many essays that had never been written, because there had been no way to publish them. Now they could be, and I was going to write them. [12]\\n\\nI've worked on several different things, but to the extent there was a turning point where I figured out what to work on, it was when I started publishing essays online. From then on I knew that whatever else I did, I'd always write essays too.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 42781, \"end_char_idx\": 44829, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"23662e81-944d-4d42-9c4f-4d130e1449e0\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"e7e9f37c-d9ff-4be3-bafb-26a29ddf48a5\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"699a057cdf104c132c2e02e04a37b680e861b5bb8b5a61c10ccda41fb40f9c34\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"9d8d94ec-e575-41f3-b8e4-5f5d952a55e9\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"225a317e9395efbffc3293b0745fc571d30a08351e8a9576cf68117a5df449eb\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Even then it took me several years to understand the implications. It meant there would be a whole new generation of essays. [11]\\n\\nIn the print era, the channel for publishing essays had been vanishingly small. Except for a few officially anointed thinkers who went to the right parties in New York, the only people allowed to publish essays were specialists writing about their specialties. There were so many essays that had never been written, because there had been no way to publish them. Now they could be, and I was going to write them. [12]\\n\\nI've worked on several different things, but to the extent there was a turning point where I figured out what to work on, it was when I started publishing essays online. From then on I knew that whatever else I did, I'd always write essays too.\\n\\nI knew that online essays would be a marginal medium at first. Socially they'd seem more like rants posted by nutjobs on their GeoCities sites than the genteel and beautifully typeset compositions published in The New Yorker. But by this point I knew enough to find that encouraging instead of discouraging.\\n\\nOne of the most conspicuous patterns I've noticed in my life is how well it has worked, for me at least, to work on things that weren't prestigious. Still life has always been the least prestigious form of painting. Viaweb and Y Combinator both seemed lame when we started them. I still get the glassy eye from strangers when they ask what I'm writing, and I explain that it's an essay I'm going to publish on my web site. Even Lisp, though prestigious intellectually in something like the way Latin is, also seems about as hip.\\n\\nIt's not that unprestigious types of work are good per se. But when you find yourself drawn to some kind of work despite its current lack of prestige, it's a sign both that there's something real to be discovered there, and that you have the right kind of motives. Impure motives are a big danger for the ambitious. If anything is going to lead you astray, it will be the desire to impress people.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 44035, \"end_char_idx\": 46067, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"9d8d94ec-e575-41f3-b8e4-5f5d952a55e9\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"23662e81-944d-4d42-9c4f-4d130e1449e0\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"462d580bfe5b4777209ae1113dfb432dcccf8a92d71a243dc1b71865280714a7\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d21a1899-f452-4e8c-9789-03e19fe1fad4\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a509faffaf4f0c8d585cc14458c0ab23f9ecf28c2ee990f2e8edb09391e9ebde\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Still life has always been the least prestigious form of painting. Viaweb and Y Combinator both seemed lame when we started them. I still get the glassy eye from strangers when they ask what I'm writing, and I explain that it's an essay I'm going to publish on my web site. Even Lisp, though prestigious intellectually in something like the way Latin is, also seems about as hip.\\n\\nIt's not that unprestigious types of work are good per se. But when you find yourself drawn to some kind of work despite its current lack of prestige, it's a sign both that there's something real to be discovered there, and that you have the right kind of motives. Impure motives are a big danger for the ambitious. If anything is going to lead you astray, it will be the desire to impress people. So while working on things that aren't prestigious doesn't guarantee you're on the right track, it at least guarantees you're not on the most common type of wrong one.\\n\\nOver the next several years I wrote lots of essays about all kinds of different topics. O'Reilly reprinted a collection of them as a book, called Hackers & Painters after one of the essays in it. I also worked on spam filters, and did some more painting. I used to have dinners for a group of friends every thursday night, which taught me how to cook for groups. And I bought another building in Cambridge, a former candy factory (and later, twas said, porn studio), to use as an office.\\n\\nOne night in October 2003 there was a big party at my house. It was a clever idea of my friend Maria Daniels, who was one of the thursday diners. Three separate hosts would all invite their friends to one party. So for every guest, two thirds of the other guests would be people they didn't know but would probably like. One of the guests was someone I didn't know but would turn out to like a lot: a woman called Jessica Livingston. A couple days later I asked her out.\\n\\nJessica was in charge of marketing at a Boston investment bank.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 45289, \"end_char_idx\": 47261, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"d21a1899-f452-4e8c-9789-03e19fe1fad4\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"9d8d94ec-e575-41f3-b8e4-5f5d952a55e9\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"ae63513247c7b5cd4694342ab12033bff8a681d5f2c527f420a4a20970b1c507\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"e03d15d7-721d-4353-a582-17fee33fe197\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fdccd334a647f675400e319ad9c55ea35129ca664b83ce2d8bb733a93f12f9ca\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I also worked on spam filters, and did some more painting. I used to have dinners for a group of friends every thursday night, which taught me how to cook for groups. And I bought another building in Cambridge, a former candy factory (and later, twas said, porn studio), to use as an office.\\n\\nOne night in October 2003 there was a big party at my house. It was a clever idea of my friend Maria Daniels, who was one of the thursday diners. Three separate hosts would all invite their friends to one party. So for every guest, two thirds of the other guests would be people they didn't know but would probably like. One of the guests was someone I didn't know but would turn out to like a lot: a woman called Jessica Livingston. A couple days later I asked her out.\\n\\nJessica was in charge of marketing at a Boston investment bank. This bank thought it understood startups, but over the next year, as she met friends of mine from the startup world, she was surprised how different reality was. And how colorful their stories were. So she decided to compile a book of interviews with startup founders.\\n\\nWhen the bank had financial problems and she had to fire half her staff, she started looking for a new job. In early 2005 she interviewed for a marketing job at a Boston VC firm. It took them weeks to make up their minds, and during this time I started telling her about all the things that needed to be fixed about venture capital. They should make a larger number of smaller investments instead of a handful of giant ones, they should be funding younger, more technical founders instead of MBAs, they should let the founders remain as CEO, and so on.\\n\\nOne of my tricks for writing essays had always been to give talks. The prospect of having to stand up in front of a group of people and tell them something that won't waste their time is a great spur to the imagination. When the Harvard Computer Society, the undergrad computer club, asked me to give a talk, I decided I would tell them how to start a startup. Maybe they'd be able to avoid the worst of the mistakes we'd made.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 46433, \"end_char_idx\": 48513, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"e03d15d7-721d-4353-a582-17fee33fe197\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d21a1899-f452-4e8c-9789-03e19fe1fad4\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"1b810a4897e9f75bf65885edfc3b80c292752ebb4102582401aed2f32ae66b64\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"da5be84a-e001-4026-8c16-952b50928df6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5736b5c45005ba18c7b4e868cd829be1c6671697cbaf725ac0d0fba84b679306\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"In early 2005 she interviewed for a marketing job at a Boston VC firm. It took them weeks to make up their minds, and during this time I started telling her about all the things that needed to be fixed about venture capital. They should make a larger number of smaller investments instead of a handful of giant ones, they should be funding younger, more technical founders instead of MBAs, they should let the founders remain as CEO, and so on.\\n\\nOne of my tricks for writing essays had always been to give talks. The prospect of having to stand up in front of a group of people and tell them something that won't waste their time is a great spur to the imagination. When the Harvard Computer Society, the undergrad computer club, asked me to give a talk, I decided I would tell them how to start a startup. Maybe they'd be able to avoid the worst of the mistakes we'd made.\\n\\nSo I gave this talk, in the course of which I told them that the best sources of seed funding were successful startup founders, because then they'd be sources of advice too. Whereupon it seemed they were all looking expectantly at me. Horrified at the prospect of having my inbox flooded by business plans (if I'd only known), I blurted out \\\"But not me!\\\" and went on with the talk. But afterward it occurred to me that I should really stop procrastinating about angel investing. I'd been meaning to since Yahoo bought us, and now it was 7 years later and I still hadn't done one angel investment.\\n\\nMeanwhile I had been scheming with Robert and Trevor about projects we could work on together. I missed working with them, and it seemed like there had to be something we could collaborate on.\\n\\nAs Jessica and I were walking home from dinner on March 11, at the corner of Garden and Walker streets, these three threads converged. Screw the VCs who were taking so long to make up their minds. We'd start our own investment firm and actually implement the ideas we'd been talking about. I'd fund it, and Jessica could quit her job and work for it, and we'd get Robert and Trevor as partners too. [13]\\n\\nOnce again, ignorance worked in our favor.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 47640, \"end_char_idx\": 49754, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"da5be84a-e001-4026-8c16-952b50928df6\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"e03d15d7-721d-4353-a582-17fee33fe197\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"4a21a45db7c4a7a0e6fe43c90a6a61129ab756d9096093e3a09f8133c5150abb\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8cba795d-13a4-41e4-a115-011b3488d17f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d5496043098f779c01fa317fbe9927d78318682846318e756dbb6b1764062942\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"and went on with the talk. But afterward it occurred to me that I should really stop procrastinating about angel investing. I'd been meaning to since Yahoo bought us, and now it was 7 years later and I still hadn't done one angel investment.\\n\\nMeanwhile I had been scheming with Robert and Trevor about projects we could work on together. I missed working with them, and it seemed like there had to be something we could collaborate on.\\n\\nAs Jessica and I were walking home from dinner on March 11, at the corner of Garden and Walker streets, these three threads converged. Screw the VCs who were taking so long to make up their minds. We'd start our own investment firm and actually implement the ideas we'd been talking about. I'd fund it, and Jessica could quit her job and work for it, and we'd get Robert and Trevor as partners too. [13]\\n\\nOnce again, ignorance worked in our favor. We had no idea how to be angel investors, and in Boston in 2005 there were no Ron Conways to learn from. So we just made what seemed like the obvious choices, and some of the things we did turned out to be novel.\\n\\nThere are multiple components to Y Combinator, and we didn't figure them all out at once. The part we got first was to be an angel firm. In those days, those two words didn't go together. There were VC firms, which were organized companies with people whose job it was to make investments, but they only did big, million dollar investments. And there were angels, who did smaller investments, but these were individuals who were usually focused on other things and made investments on the side. And neither of them helped founders enough in the beginning. We knew how helpless founders were in some respects, because we remembered how helpless we'd been. For example, one thing Julian had done for us that seemed to us like magic was to get us set up as a company. We were fine writing fairly difficult software, but actually getting incorporated, with bylaws and stock and all that stuff, how on earth did you do that? Our plan was not only to make seed investments, but to do for startups everything Julian had done for us.\\n\\nYC was not organized as a fund.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 48870, \"end_char_idx\": 51027, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"8cba795d-13a4-41e4-a115-011b3488d17f\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"da5be84a-e001-4026-8c16-952b50928df6\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"42698ddd5167628eaf4c1ecc4f434498fec5a3b3817f8e079cce7d962ab601e9\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"2691af43-bfd3-470b-a5a6-21d07f2fe69a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5a2b5fa8da98bba5bfb1824cdd6ea82eb3e0d8336fa4613d0d1d0d475e863e18\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"In those days, those two words didn't go together. There were VC firms, which were organized companies with people whose job it was to make investments, but they only did big, million dollar investments. And there were angels, who did smaller investments, but these were individuals who were usually focused on other things and made investments on the side. And neither of them helped founders enough in the beginning. We knew how helpless founders were in some respects, because we remembered how helpless we'd been. For example, one thing Julian had done for us that seemed to us like magic was to get us set up as a company. We were fine writing fairly difficult software, but actually getting incorporated, with bylaws and stock and all that stuff, how on earth did you do that? Our plan was not only to make seed investments, but to do for startups everything Julian had done for us.\\n\\nYC was not organized as a fund. It was cheap enough to run that we funded it with our own money. That went right by 99% of readers, but professional investors are thinking \\\"Wow, that means they got all the returns.\\\" But once again, this was not due to any particular insight on our part. We didn't know how VC firms were organized. It never occurred to us to try to raise a fund, and if it had, we wouldn't have known where to start. [14]\\n\\nThe most distinctive thing about YC is the batch model: to fund a bunch of startups all at once, twice a year, and then to spend three months focusing intensively on trying to help them. That part we discovered by accident, not merely implicitly but explicitly due to our ignorance about investing. We needed to get experience as investors. What better way, we thought, than to fund a whole bunch of startups at once? We knew undergrads got temporary jobs at tech companies during the summer. Why not organize a summer program where they'd start startups instead? We wouldn't feel guilty for being in a sense fake investors, because they would in a similar sense be fake founders.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 50106, \"end_char_idx\": 52116, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"2691af43-bfd3-470b-a5a6-21d07f2fe69a\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8cba795d-13a4-41e4-a115-011b3488d17f\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"913e905616cfe7a697cb00b92f99a63c28f26159d55f2497ca5a471b2a6dab71\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"7be8ceb5-3c54-4e92-a0be-0ac762809326\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"98ee2f1f7368c99b257d71767b8d7960dd917a003f02c852b993662fa7f59427\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"We didn't know how VC firms were organized. It never occurred to us to try to raise a fund, and if it had, we wouldn't have known where to start. [14]\\n\\nThe most distinctive thing about YC is the batch model: to fund a bunch of startups all at once, twice a year, and then to spend three months focusing intensively on trying to help them. That part we discovered by accident, not merely implicitly but explicitly due to our ignorance about investing. We needed to get experience as investors. What better way, we thought, than to fund a whole bunch of startups at once? We knew undergrads got temporary jobs at tech companies during the summer. Why not organize a summer program where they'd start startups instead? We wouldn't feel guilty for being in a sense fake investors, because they would in a similar sense be fake founders. So while we probably wouldn't make much money out of it, we'd at least get to practice being investors on them, and they for their part would probably have a more interesting summer than they would working at Microsoft.\\n\\nWe'd use the building I owned in Cambridge as our headquarters. We'd all have dinner there once a week \\u2014 on tuesdays, since I was already cooking for the thursday diners on thursdays \\u2014 and after dinner we'd bring in experts on startups to give talks.\\n\\nWe knew undergrads were deciding then about summer jobs, so in a matter of days we cooked up something we called the Summer Founders Program, and I posted an announcement on my site, inviting undergrads to apply. I had never imagined that writing essays would be a way to get \\\"deal flow,\\\" as investors call it, but it turned out to be the perfect source. [15] We got 225 applications for the Summer Founders Program, and we were surprised to find that a lot of them were from people who'd already graduated, or were about to that spring. Already this SFP thing was starting to feel more serious than we'd intended.\\n\\nWe invited about 20 of the 225 groups to interview in person, and from those we picked 8 to fund. They were an impressive group.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 51284, \"end_char_idx\": 53334, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"7be8ceb5-3c54-4e92-a0be-0ac762809326\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"2691af43-bfd3-470b-a5a6-21d07f2fe69a\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"dd76b283092cbad59f559af33194a758b81ea618d6a912443a98a76433a802c5\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d5a90442-ad94-45d3-9359-90c9c65238a1\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b98ca7134e302efdd66454a76a21a8ca8e4b2b1df285be5b345b01cbb3ca3aa0\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"We knew undergrads were deciding then about summer jobs, so in a matter of days we cooked up something we called the Summer Founders Program, and I posted an announcement on my site, inviting undergrads to apply. I had never imagined that writing essays would be a way to get \\\"deal flow,\\\" as investors call it, but it turned out to be the perfect source. [15] We got 225 applications for the Summer Founders Program, and we were surprised to find that a lot of them were from people who'd already graduated, or were about to that spring. Already this SFP thing was starting to feel more serious than we'd intended.\\n\\nWe invited about 20 of the 225 groups to interview in person, and from those we picked 8 to fund. They were an impressive group. That first batch included reddit, Justin Kan and Emmett Shear, who went on to found Twitch, Aaron Swartz, who had already helped write the RSS spec and would a few years later become a martyr for open access, and Sam Altman, who would later become the second president of YC. I don't think it was entirely luck that the first batch was so good. You had to be pretty bold to sign up for a weird thing like the Summer Founders Program instead of a summer job at a legit place like Microsoft or Goldman Sachs.\\n\\nThe deal for startups was based on a combination of the deal we did with Julian ($10k for 10%) and what Robert said MIT grad students got for the summer ($6k). We invested $6k per founder, which in the typical two-founder case was $12k, in return for 6%. That had to be fair, because it was twice as good as the deal we ourselves had taken. Plus that first summer, which was really hot, Jessica brought the founders free air conditioners. [16]\\n\\nFairly quickly I realized that we had stumbled upon the way to scale startup funding. Funding startups in batches was more convenient for us, because it meant we could do things for a lot of startups at once, but being part of a batch was better for the startups too. It solved one of the biggest problems faced by founders: the isolation.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 52590, \"end_char_idx\": 54627, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"d5a90442-ad94-45d3-9359-90c9c65238a1\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"7be8ceb5-3c54-4e92-a0be-0ac762809326\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"24fd1cbf2a10a17b312a0d0ed137ab12bd64b475bfddaaa82ec4fc415df9ed0c\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"38f59e06-74f1-43f3-8476-6e317247b136\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"4ea12277cb617234eef0a600730251818ff71825d947d4eafb811e706cb87a12\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"The deal for startups was based on a combination of the deal we did with Julian ($10k for 10%) and what Robert said MIT grad students got for the summer ($6k). We invested $6k per founder, which in the typical two-founder case was $12k, in return for 6%. That had to be fair, because it was twice as good as the deal we ourselves had taken. Plus that first summer, which was really hot, Jessica brought the founders free air conditioners. [16]\\n\\nFairly quickly I realized that we had stumbled upon the way to scale startup funding. Funding startups in batches was more convenient for us, because it meant we could do things for a lot of startups at once, but being part of a batch was better for the startups too. It solved one of the biggest problems faced by founders: the isolation. Now you not only had colleagues, but colleagues who understood the problems you were facing and could tell you how they were solving them.\\n\\nAs YC grew, we started to notice other advantages of scale. The alumni became a tight community, dedicated to helping one another, and especially the current batch, whose shoes they remembered being in. We also noticed that the startups were becoming one another's customers. We used to refer jokingly to the \\\"YC GDP,\\\" but as YC grows this becomes less and less of a joke. Now lots of startups get their initial set of customers almost entirely from among their batchmates.\\n\\nI had not originally intended YC to be a full-time job. I was going to do three things: hack, write essays, and work on YC. As YC grew, and I grew more excited about it, it started to take up a lot more than a third of my attention. But for the first few years I was still able to work on other things.\\n\\nIn the summer of 2006, Robert and I started working on a new version of Arc. This one was reasonably fast, because it was compiled into Scheme. To test this new Arc, I wrote Hacker News in it. It was originally meant to be a news aggregator for startup founders and was called Startup News, but after a few months I got tired of reading about nothing but startups.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 53843, \"end_char_idx\": 55911, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"38f59e06-74f1-43f3-8476-6e317247b136\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d5a90442-ad94-45d3-9359-90c9c65238a1\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"b8f4c44cc6bd1c38e8266d4fb246e107b1764291f78dc42e376758f9a7966e66\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"e80c67bd-8b25-4c41-8270-419e7608f566\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"c83d8c4aefd73c6a0fb2dbdec62bfc2ddf271fa4a84b7800943297d89211082b\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Now lots of startups get their initial set of customers almost entirely from among their batchmates.\\n\\nI had not originally intended YC to be a full-time job. I was going to do three things: hack, write essays, and work on YC. As YC grew, and I grew more excited about it, it started to take up a lot more than a third of my attention. But for the first few years I was still able to work on other things.\\n\\nIn the summer of 2006, Robert and I started working on a new version of Arc. This one was reasonably fast, because it was compiled into Scheme. To test this new Arc, I wrote Hacker News in it. It was originally meant to be a news aggregator for startup founders and was called Startup News, but after a few months I got tired of reading about nothing but startups. Plus it wasn't startup founders we wanted to reach. It was future startup founders. So I changed the name to Hacker News and the topic to whatever engaged one's intellectual curiosity.\\n\\nHN was no doubt good for YC, but it was also by far the biggest source of stress for me. If all I'd had to do was select and help founders, life would have been so easy. And that implies that HN was a mistake. Surely the biggest source of stress in one's work should at least be something close to the core of the work. Whereas I was like someone who was in pain while running a marathon not from the exertion of running, but because I had a blister from an ill-fitting shoe. When I was dealing with some urgent problem during YC, there was about a 60% chance it had to do with HN, and a 40% chance it had do with everything else combined. [17]\\n\\nAs well as HN, I wrote all of YC's internal software in Arc. But while I continued to work a good deal in Arc, I gradually stopped working on Arc, partly because I didn't have time to, and partly because it was a lot less attractive to mess around with the language now that we had all this infrastructure depending on it. So now my three projects were reduced to two: writing essays and working on YC.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 55141, \"end_char_idx\": 57146, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"e80c67bd-8b25-4c41-8270-419e7608f566\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"38f59e06-74f1-43f3-8476-6e317247b136\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"ece3fbac1589be7d3dcb70ddd966a229f7c8bb4d5e33558c426e9c545136803a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"86664452-ed53-4b15-8f57-bd7c8ee6c10b\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"7e3e495163fb5eb09cd32bb6e723539877dd4ee7694aa788b68746ee3c1e65f7\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Surely the biggest source of stress in one's work should at least be something close to the core of the work. Whereas I was like someone who was in pain while running a marathon not from the exertion of running, but because I had a blister from an ill-fitting shoe. When I was dealing with some urgent problem during YC, there was about a 60% chance it had to do with HN, and a 40% chance it had do with everything else combined. [17]\\n\\nAs well as HN, I wrote all of YC's internal software in Arc. But while I continued to work a good deal in Arc, I gradually stopped working on Arc, partly because I didn't have time to, and partly because it was a lot less attractive to mess around with the language now that we had all this infrastructure depending on it. So now my three projects were reduced to two: writing essays and working on YC.\\n\\nYC was different from other kinds of work I've done. Instead of deciding for myself what to work on, the problems came to me. Every 6 months there was a new batch of startups, and their problems, whatever they were, became our problems. It was very engaging work, because their problems were quite varied, and the good founders were very effective. If you were trying to learn the most you could about startups in the shortest possible time, you couldn't have picked a better way to do it.\\n\\nThere were parts of the job I didn't like. Disputes between cofounders, figuring out when people were lying to us, fighting with people who maltreated the startups, and so on. But I worked hard even at the parts I didn't like. I was haunted by something Kevin Hale once said about companies: \\\"No one works harder than the boss.\\\" He meant it both descriptively and prescriptively, and it was the second part that scared me. I wanted YC to be good, so if how hard I worked set the upper bound on how hard everyone else worked, I'd better work very hard.\\n\\nOne day in 2010, when he was visiting California for interviews, Robert Morris did something astonishing: he offered me unsolicited advice. I can only remember him doing that once before.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 56308, \"end_char_idx\": 58379, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"86664452-ed53-4b15-8f57-bd7c8ee6c10b\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"e80c67bd-8b25-4c41-8270-419e7608f566\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"5d38d666dcb1e1de7aaee15c1d660e1c5740fb1f98f3b6e56815e4fb036cc52a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d4df4c4a-f27c-4f1f-8b2a-e70335ba1c3a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5c92a4f24855f094d07f5dfefa201bd5953f58d8a34629ba9754d11cf7b83327\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"There were parts of the job I didn't like. Disputes between cofounders, figuring out when people were lying to us, fighting with people who maltreated the startups, and so on. But I worked hard even at the parts I didn't like. I was haunted by something Kevin Hale once said about companies: \\\"No one works harder than the boss.\\\" He meant it both descriptively and prescriptively, and it was the second part that scared me. I wanted YC to be good, so if how hard I worked set the upper bound on how hard everyone else worked, I'd better work very hard.\\n\\nOne day in 2010, when he was visiting California for interviews, Robert Morris did something astonishing: he offered me unsolicited advice. I can only remember him doing that once before. One day at Viaweb, when I was bent over double from a kidney stone, he suggested that it would be a good idea for him to take me to the hospital. That was what it took for Rtm to offer unsolicited advice. So I remember his exact words very clearly. \\\"You know,\\\" he said, \\\"you should make sure Y Combinator isn't the last cool thing you do.\\\"\\n\\nAt the time I didn't understand what he meant, but gradually it dawned on me that he was saying I should quit. This seemed strange advice, because YC was doing great. But if there was one thing rarer than Rtm offering advice, it was Rtm being wrong. So this set me thinking. It was true that on my current trajectory, YC would be the last thing I did, because it was only taking up more of my attention. It had already eaten Arc, and was in the process of eating essays too. Either YC was my life's work or I'd have to leave eventually. And it wasn't, so I would.\\n\\nIn the summer of 2012 my mother had a stroke, and the cause turned out to be a blood clot caused by colon cancer. The stroke destroyed her balance, and she was put in a nursing home, but she really wanted to get out of it and back to her house, and my sister and I were determined to help her do it.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 57639, \"end_char_idx\": 59585, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"d4df4c4a-f27c-4f1f-8b2a-e70335ba1c3a\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"86664452-ed53-4b15-8f57-bd7c8ee6c10b\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"1594931a9bceeb1da08deed6784382f21ccf111972b99b74914e15e817fa62c9\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"3ceacf79-f9cb-4030-8812-dafd26a8a41a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5a709f0506f1caa8ae849edcd404835d1e9712c53997c04e7dac0d8965d2888b\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"This seemed strange advice, because YC was doing great. But if there was one thing rarer than Rtm offering advice, it was Rtm being wrong. So this set me thinking. It was true that on my current trajectory, YC would be the last thing I did, because it was only taking up more of my attention. It had already eaten Arc, and was in the process of eating essays too. Either YC was my life's work or I'd have to leave eventually. And it wasn't, so I would.\\n\\nIn the summer of 2012 my mother had a stroke, and the cause turned out to be a blood clot caused by colon cancer. The stroke destroyed her balance, and she was put in a nursing home, but she really wanted to get out of it and back to her house, and my sister and I were determined to help her do it. I used to fly up to Oregon to visit her regularly, and I had a lot of time to think on those flights. On one of them I realized I was ready to hand YC over to someone else.\\n\\nI asked Jessica if she wanted to be president, but she didn't, so we decided we'd try to recruit Sam Altman. We talked to Robert and Trevor and we agreed to make it a complete changing of the guard. Up till that point YC had been controlled by the original LLC we four had started. But we wanted YC to last for a long time, and to do that it couldn't be controlled by the founders. So if Sam said yes, we'd let him reorganize YC. Robert and I would retire, and Jessica and Trevor would become ordinary partners.\\n\\nWhen we asked Sam if he wanted to be president of YC, initially he said no. He wanted to start a startup to make nuclear reactors. But I kept at it, and in October 2013 he finally agreed. We decided he'd take over starting with the winter 2014 batch. For the rest of 2013 I left running YC more and more to Sam, partly so he could learn the job, and partly because I was focused on my mother, whose cancer had returned.\\n\\nShe died on January 15, 2014.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 58832, \"end_char_idx\": 60723, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"3ceacf79-f9cb-4030-8812-dafd26a8a41a\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d4df4c4a-f27c-4f1f-8b2a-e70335ba1c3a\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"4ba635f286a1dd478401957be687bb24d681b40b660194f7920a66bd964cbd53\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"c49a0ab2-a5fd-4ac6-98b8-9ebcd08065f6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9d8368efe0a15a61ddad51b11eb9fc4d60c42cd211cbeaeb7b15ad676d02ea6a\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Up till that point YC had been controlled by the original LLC we four had started. But we wanted YC to last for a long time, and to do that it couldn't be controlled by the founders. So if Sam said yes, we'd let him reorganize YC. Robert and I would retire, and Jessica and Trevor would become ordinary partners.\\n\\nWhen we asked Sam if he wanted to be president of YC, initially he said no. He wanted to start a startup to make nuclear reactors. But I kept at it, and in October 2013 he finally agreed. We decided he'd take over starting with the winter 2014 batch. For the rest of 2013 I left running YC more and more to Sam, partly so he could learn the job, and partly because I was focused on my mother, whose cancer had returned.\\n\\nShe died on January 15, 2014. We knew this was coming, but it was still hard when it did.\\n\\nI kept working on YC till March, to help get that batch of startups through Demo Day, then I checked out pretty completely. (I still talk to alumni and to new startups working on things I'm interested in, but that only takes a few hours a week.)\\n\\nWhat should I do next? Rtm's advice hadn't included anything about that. I wanted to do something completely different, so I decided I'd paint. I wanted to see how good I could get if I really focused on it. So the day after I stopped working on YC, I started painting. I was rusty and it took a while to get back into shape, but it was at least completely engaging. [18]\\n\\nI spent most of the rest of 2014 painting. I'd never been able to work so uninterruptedly before, and I got to be better than I had been. Not good enough, but better. Then in November, right in the middle of a painting, I ran out of steam. Up till that point I'd always been curious to see how the painting I was working on would turn out, but suddenly finishing this one seemed like a chore. So I stopped working on it and cleaned my brushes and haven't painted since. So far anyway.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 59959, \"end_char_idx\": 61889, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"c49a0ab2-a5fd-4ac6-98b8-9ebcd08065f6\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"3ceacf79-f9cb-4030-8812-dafd26a8a41a\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"68cb67a6ea73d0b85669483fefc16845ef036b4ecf2717b75c6c307ad14e7bc0\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"0bdc8d30-992a-441f-a4b7-062c3c1d9318\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"c7d5bdb18cbe3eea23f960f647f285ca859c1e725798dceabe7320f24f4882de\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I wanted to do something completely different, so I decided I'd paint. I wanted to see how good I could get if I really focused on it. So the day after I stopped working on YC, I started painting. I was rusty and it took a while to get back into shape, but it was at least completely engaging. [18]\\n\\nI spent most of the rest of 2014 painting. I'd never been able to work so uninterruptedly before, and I got to be better than I had been. Not good enough, but better. Then in November, right in the middle of a painting, I ran out of steam. Up till that point I'd always been curious to see how the painting I was working on would turn out, but suddenly finishing this one seemed like a chore. So I stopped working on it and cleaned my brushes and haven't painted since. So far anyway.\\n\\nI realize that sounds rather wimpy. But attention is a zero sum game. If you can choose what to work on, and you choose a project that's not the best one (or at least a good one) for you, then it's getting in the way of another project that is. And at 50 there was some opportunity cost to screwing around.\\n\\nI started writing essays again, and wrote a bunch of new ones over the next few months. I even wrote a couple that weren't about startups. Then in March 2015 I started working on Lisp again.\\n\\nThe distinctive thing about Lisp is that its core is a language defined by writing an interpreter in itself. It wasn't originally intended as a programming language in the ordinary sense. It was meant to be a formal model of computation, an alternative to the Turing machine. If you want to write an interpreter for a language in itself, what's the minimum set of predefined operators you need? The Lisp that John McCarthy invented, or more accurately discovered, is an answer to that question. [19]\\n\\nMcCarthy didn't realize this Lisp could even be used to program computers till his grad student Steve Russell suggested it. Russell translated McCarthy's interpreter into IBM 704 machine language, and from that point Lisp started also to be a programming language in the ordinary sense.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 61105, \"end_char_idx\": 63178, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"0bdc8d30-992a-441f-a4b7-062c3c1d9318\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"c49a0ab2-a5fd-4ac6-98b8-9ebcd08065f6\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"afcb130280ace1160f49febe0b88a1512f7caf7dcd1a072b1190513757a4c962\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"af71f827-9a71-49a2-a65f-07a04d606b71\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"8f475e27dafa3b0501476cd1ffa24d8df7d7c172d9740362edb97ae7370d4704\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I even wrote a couple that weren't about startups. Then in March 2015 I started working on Lisp again.\\n\\nThe distinctive thing about Lisp is that its core is a language defined by writing an interpreter in itself. It wasn't originally intended as a programming language in the ordinary sense. It was meant to be a formal model of computation, an alternative to the Turing machine. If you want to write an interpreter for a language in itself, what's the minimum set of predefined operators you need? The Lisp that John McCarthy invented, or more accurately discovered, is an answer to that question. [19]\\n\\nMcCarthy didn't realize this Lisp could even be used to program computers till his grad student Steve Russell suggested it. Russell translated McCarthy's interpreter into IBM 704 machine language, and from that point Lisp started also to be a programming language in the ordinary sense. But its origins as a model of computation gave it a power and elegance that other languages couldn't match. It was this that attracted me in college, though I didn't understand why at the time.\\n\\nMcCarthy's 1960 Lisp did nothing more than interpret Lisp expressions. It was missing a lot of things you'd want in a programming language. So these had to be added, and when they were, they weren't defined using McCarthy's original axiomatic approach. That wouldn't have been feasible at the time. McCarthy tested his interpreter by hand-simulating the execution of programs. But it was already getting close to the limit of interpreters you could test that way \\u2014 indeed, there was a bug in it that McCarthy had overlooked. To test a more complicated interpreter, you'd have had to run it, and computers then weren't powerful enough.\\n\\nNow they are, though. Now you could continue using McCarthy's axiomatic approach till you'd defined a complete programming language. And as long as every change you made to McCarthy's Lisp was a discoveredness-preserving transformation, you could, in principle, end up with a complete language that had this quality. Harder to do than to talk about, of course, but if it was possible in principle, why not try? So I decided to take a shot at it.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 62287, \"end_char_idx\": 64455, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"af71f827-9a71-49a2-a65f-07a04d606b71\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"0bdc8d30-992a-441f-a4b7-062c3c1d9318\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"86d7cba1eab7fb1894d071c24596e1474081198fc7deb26f6efe13ca4a8960ca\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"238fce0f-f3e9-464e-9d72-62a0d76abaa3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b7ef058aeafad36fb12e177cf4cec90e27085c72137e2116c876f152b47dc90e\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"That wouldn't have been feasible at the time. McCarthy tested his interpreter by hand-simulating the execution of programs. But it was already getting close to the limit of interpreters you could test that way \\u2014 indeed, there was a bug in it that McCarthy had overlooked. To test a more complicated interpreter, you'd have had to run it, and computers then weren't powerful enough.\\n\\nNow they are, though. Now you could continue using McCarthy's axiomatic approach till you'd defined a complete programming language. And as long as every change you made to McCarthy's Lisp was a discoveredness-preserving transformation, you could, in principle, end up with a complete language that had this quality. Harder to do than to talk about, of course, but if it was possible in principle, why not try? So I decided to take a shot at it. It took 4 years, from March 26, 2015 to October 12, 2019. It was fortunate that I had a precisely defined goal, or it would have been hard to keep at it for so long.\\n\\nI wrote this new Lisp, called Bel, in itself in Arc. That may sound like a contradiction, but it's an indication of the sort of trickery I had to engage in to make this work. By means of an egregious collection of hacks I managed to make something close enough to an interpreter written in itself that could actually run. Not fast, but fast enough to test.\\n\\nI had to ban myself from writing essays during most of this time, or I'd never have finished. In late 2015 I spent 3 months writing essays, and when I went back to working on Bel I could barely understand the code. Not so much because it was badly written as because the problem is so convoluted. When you're working on an interpreter written in itself, it's hard to keep track of what's happening at what level, and errors can be practically encrypted by the time you get them.\\n\\nSo I said no more essays till Bel was done. But I told few people about Bel while I was working on it. So for years it must have seemed that I was doing nothing, when in fact I was working harder than I'd ever worked on anything.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 63627, \"end_char_idx\": 65690, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"238fce0f-f3e9-464e-9d72-62a0d76abaa3\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"af71f827-9a71-49a2-a65f-07a04d606b71\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"3809aef93ae256c7d9db6bd1903093111b7c517f9d767b9f615a926a602016a3\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"6ab3abc5-1a70-4e47-95d7-87f27c9322ab\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"52521ada44571b1fe924e808ee296fcb02b8086a5b012d8ba43b7eefe868492a\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Not fast, but fast enough to test.\\n\\nI had to ban myself from writing essays during most of this time, or I'd never have finished. In late 2015 I spent 3 months writing essays, and when I went back to working on Bel I could barely understand the code. Not so much because it was badly written as because the problem is so convoluted. When you're working on an interpreter written in itself, it's hard to keep track of what's happening at what level, and errors can be practically encrypted by the time you get them.\\n\\nSo I said no more essays till Bel was done. But I told few people about Bel while I was working on it. So for years it must have seemed that I was doing nothing, when in fact I was working harder than I'd ever worked on anything. Occasionally after wrestling for hours with some gruesome bug I'd check Twitter or HN and see someone asking \\\"Does Paul Graham still code?\\\"\\n\\nWorking on Bel was hard but satisfying. I worked on it so intensively that at any given time I had a decent chunk of the code in my head and could write more there. I remember taking the boys to the coast on a sunny day in 2015 and figuring out how to deal with some problem involving continuations while I watched them play in the tide pools. It felt like I was doing life right. I remember that because I was slightly dismayed at how novel it felt. The good news is that I had more moments like this over the next few years.\\n\\nIn the summer of 2016 we moved to England. We wanted our kids to see what it was like living in another country, and since I was a British citizen by birth, that seemed the obvious choice. We only meant to stay for a year, but we liked it so much that we still live there. So most of Bel was written in England.\\n\\nIn the fall of 2019, Bel was finally finished. Like McCarthy's original Lisp, it's a spec rather than an implementation, although like McCarthy's Lisp it's a spec expressed as code.\\n\\nNow that I could write essays again, I wrote a bunch about topics I'd had stacked up.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 64945, \"end_char_idx\": 66941, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"6ab3abc5-1a70-4e47-95d7-87f27c9322ab\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"238fce0f-f3e9-464e-9d72-62a0d76abaa3\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"1bbaefc1544a290425d2d131f4f9ba5119b144a83fad49a130a0f075d1f51b73\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"23085fab-e20b-40fa-9e7e-9f8623bfdee3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"03644354f0b71aa93d396c1a8cdc0b67b129e1b5d3e2c2955c7d8bf8f35c711f\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"It felt like I was doing life right. I remember that because I was slightly dismayed at how novel it felt. The good news is that I had more moments like this over the next few years.\\n\\nIn the summer of 2016 we moved to England. We wanted our kids to see what it was like living in another country, and since I was a British citizen by birth, that seemed the obvious choice. We only meant to stay for a year, but we liked it so much that we still live there. So most of Bel was written in England.\\n\\nIn the fall of 2019, Bel was finally finished. Like McCarthy's original Lisp, it's a spec rather than an implementation, although like McCarthy's Lisp it's a spec expressed as code.\\n\\nNow that I could write essays again, I wrote a bunch about topics I'd had stacked up. I kept writing essays through 2020, but I also started to think about other things I could work on. How should I choose what to do? Well, how had I chosen what to work on in the past? I wrote an essay for myself to answer that question, and I was surprised how long and messy the answer turned out to be. If this surprised me, who'd lived it, then I thought perhaps it would be interesting to other people, and encouraging to those with similarly messy lives. So I wrote a more detailed version for others to read, and this is the last sentence of it.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nNotes\\n\\n[1] My experience skipped a step in the evolution of computers: time-sharing machines with interactive OSes. I went straight from batch processing to microcomputers, which made microcomputers seem all the more exciting.\\n\\n[2] Italian words for abstract concepts can nearly always be predicted from their English cognates (except for occasional traps like polluzione). It's the everyday words that differ. So if you string together a lot of abstract concepts with a few simple verbs, you can make a little Italian go a long way.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 66176, \"end_char_idx\": 68036, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"23085fab-e20b-40fa-9e7e-9f8623bfdee3\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"6ab3abc5-1a70-4e47-95d7-87f27c9322ab\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"e2577e4f41178deb5be01e0d41a84e1ba0de00a3aea71148ef7483bce93882aa\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"a62f3185-c899-46d2-95a6-ddc24b22492a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fa0bf919caab7a97086ff3de911eff4b4aa409875a9db34b8fb095966e480c67\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"I wrote an essay for myself to answer that question, and I was surprised how long and messy the answer turned out to be. If this surprised me, who'd lived it, then I thought perhaps it would be interesting to other people, and encouraging to those with similarly messy lives. So I wrote a more detailed version for others to read, and this is the last sentence of it.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nNotes\\n\\n[1] My experience skipped a step in the evolution of computers: time-sharing machines with interactive OSes. I went straight from batch processing to microcomputers, which made microcomputers seem all the more exciting.\\n\\n[2] Italian words for abstract concepts can nearly always be predicted from their English cognates (except for occasional traps like polluzione). It's the everyday words that differ. So if you string together a lot of abstract concepts with a few simple verbs, you can make a little Italian go a long way.\\n\\n[3] I lived at Piazza San Felice 4, so my walk to the Accademia went straight down the spine of old Florence: past the Pitti, across the bridge, past Orsanmichele, between the Duomo and the Baptistery, and then up Via Ricasoli to Piazza San Marco. I saw Florence at street level in every possible condition, from empty dark winter evenings to sweltering summer days when the streets were packed with tourists.\\n\\n[4] You can of course paint people like still lives if you want to, and they're willing. That sort of portrait is arguably the apex of still life painting, though the long sitting does tend to produce pained expressions in the sitters.\\n\\n[5] Interleaf was one of many companies that had smart people and built impressive technology, and yet got crushed by Moore's Law. In the 1990s the exponential growth in the power of commodity (i.e. Intel) processors rolled up high-end, special-purpose hardware and software companies like a bulldozer.\\n\\n[6] The signature style seekers at RISD weren't specifically mercenary. In the art world, money and coolness are tightly coupled. Anything expensive comes to be seen as cool, and anything seen as cool will soon become equally expensive.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 67126, \"end_char_idx\": 69226, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"a62f3185-c899-46d2-95a6-ddc24b22492a\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"23085fab-e20b-40fa-9e7e-9f8623bfdee3\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"ad2e407e509b61dc26bffc6c6e643417c9976fbee7a964f3d0a1d14643550d50\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"80349e51-f072-455d-937c-94856e710e50\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fc87f646c3f02ab27a88222e08bb3dab2ab7f685cde5407e9fb3cdae23c3ce08\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"[4] You can of course paint people like still lives if you want to, and they're willing. That sort of portrait is arguably the apex of still life painting, though the long sitting does tend to produce pained expressions in the sitters.\\n\\n[5] Interleaf was one of many companies that had smart people and built impressive technology, and yet got crushed by Moore's Law. In the 1990s the exponential growth in the power of commodity (i.e. Intel) processors rolled up high-end, special-purpose hardware and software companies like a bulldozer.\\n\\n[6] The signature style seekers at RISD weren't specifically mercenary. In the art world, money and coolness are tightly coupled. Anything expensive comes to be seen as cool, and anything seen as cool will soon become equally expensive.\\n\\n[7] Technically the apartment wasn't rent-controlled but rent-stabilized, but this is a refinement only New Yorkers would know or care about. The point is that it was really cheap, less than half market price.\\n\\n[8] Most software you can launch as soon as it's done. But when the software is an online store builder and you're hosting the stores, if you don't have any users yet, that fact will be painfully obvious. So before we could launch publicly we had to launch privately, in the sense of recruiting an initial set of users and making sure they had decent-looking stores.\\n\\n[9] We'd had a code editor in Viaweb for users to define their own page styles. They didn't know it, but they were editing Lisp expressions underneath. But this wasn't an app editor, because the code ran when the merchants' sites were generated, not when shoppers visited them.\\n\\n[10] This was the first instance of what is now a familiar experience, and so was what happened next, when I read the comments and found they were full of angry people. How could I claim that Lisp was better than other languages? Weren't they all Turing complete? People who see the responses to essays I write sometimes tell me how sorry they feel for me, but I'm not exaggerating when I reply that it has always been like this, since the very beginning. It comes with the territory.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 68449, \"end_char_idx\": 70570, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"80349e51-f072-455d-937c-94856e710e50\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"a62f3185-c899-46d2-95a6-ddc24b22492a\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"92ef2f67017b2223fdfa7dda21751481d8eefe9ebeda70474484c1e13316fc26\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"ae534e05-5f13-4bb8-8228-a66ec5b571b1\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f934b13d96a62cc4b38b94f8b98d58ee90ff432e4a67eb8e9dd5f1ca0a2db2b6\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"[9] We'd had a code editor in Viaweb for users to define their own page styles. They didn't know it, but they were editing Lisp expressions underneath. But this wasn't an app editor, because the code ran when the merchants' sites were generated, not when shoppers visited them.\\n\\n[10] This was the first instance of what is now a familiar experience, and so was what happened next, when I read the comments and found they were full of angry people. How could I claim that Lisp was better than other languages? Weren't they all Turing complete? People who see the responses to essays I write sometimes tell me how sorry they feel for me, but I'm not exaggerating when I reply that it has always been like this, since the very beginning. It comes with the territory. An essay must tell readers things they don't already know, and some people dislike being told such things.\\n\\n[11] People put plenty of stuff on the internet in the 90s of course, but putting something online is not the same as publishing it online. Publishing online means you treat the online version as the (or at least a) primary version.\\n\\n[12] There is a general lesson here that our experience with Y Combinator also teaches: Customs continue to constrain you long after the restrictions that caused them have disappeared. Customary VC practice had once, like the customs about publishing essays, been based on real constraints. Startups had once been much more expensive to start, and proportionally rare. Now they could be cheap and common, but the VCs' customs still reflected the old world, just as customs about writing essays still reflected the constraints of the print era.\\n\\nWhich in turn implies that people who are independent-minded (i.e. less influenced by custom) will have an advantage in fields affected by rapid change (where customs are more likely to be obsolete).\\n\\nHere's an interesting point, though: you can't always predict which fields will be affected by rapid change. Obviously software and venture capital will be, but who would have predicted that essay writing would be?\\n\\n[13] Y Combinator was not the original name. At first we were called Cambridge Seed.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 69807, \"end_char_idx\": 71959, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"ae534e05-5f13-4bb8-8228-a66ec5b571b1\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"80349e51-f072-455d-937c-94856e710e50\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"687715ebc13fbcac526b4f4aeba472ae3f27f03b14511887f86a458058546cea\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"9af945f0-0da7-4825-8672-3a9e6bc81944\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"6b359af8b720bc4d194e5fc12400e26f6938d772408253dab57c8ad54eb925b3\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Customary VC practice had once, like the customs about publishing essays, been based on real constraints. Startups had once been much more expensive to start, and proportionally rare. Now they could be cheap and common, but the VCs' customs still reflected the old world, just as customs about writing essays still reflected the constraints of the print era.\\n\\nWhich in turn implies that people who are independent-minded (i.e. less influenced by custom) will have an advantage in fields affected by rapid change (where customs are more likely to be obsolete).\\n\\nHere's an interesting point, though: you can't always predict which fields will be affected by rapid change. Obviously software and venture capital will be, but who would have predicted that essay writing would be?\\n\\n[13] Y Combinator was not the original name. At first we were called Cambridge Seed. But we didn't want a regional name, in case someone copied us in Silicon Valley, so we renamed ourselves after one of the coolest tricks in the lambda calculus, the Y combinator.\\n\\nI picked orange as our color partly because it's the warmest, and partly because no VC used it. In 2005 all the VCs used staid colors like maroon, navy blue, and forest green, because they were trying to appeal to LPs, not founders. The YC logo itself is an inside joke: the Viaweb logo had been a white V on a red circle, so I made the YC logo a white Y on an orange square.\\n\\n[14] YC did become a fund for a couple years starting in 2009, because it was getting so big I could no longer afford to fund it personally. But after Heroku got bought we had enough money to go back to being self-funded.\\n\\n[15] I've never liked the term \\\"deal flow,\\\" because it implies that the number of new startups at any given time is fixed. This is not only false, but it's the purpose of YC to falsify it, by causing startups to be founded that would not otherwise have existed.\\n\\n[16] She reports that they were all different shapes and sizes, because there was a run on air conditioners and she had to get whatever she could, but that they were all heavier than she could carry now.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 71098, \"end_char_idx\": 73206, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"9af945f0-0da7-4825-8672-3a9e6bc81944\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"ae534e05-5f13-4bb8-8228-a66ec5b571b1\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"b0e74bec64deb6511016891e0ccad4029368422fc3826d4fc73942ea4df98696\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"536690a5-862d-432b-b9b6-6050dc22aba0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"6f287446f820c084eee0ae66893bad1c5ca6714c78a6045e064ac013ec184c5c\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"The YC logo itself is an inside joke: the Viaweb logo had been a white V on a red circle, so I made the YC logo a white Y on an orange square.\\n\\n[14] YC did become a fund for a couple years starting in 2009, because it was getting so big I could no longer afford to fund it personally. But after Heroku got bought we had enough money to go back to being self-funded.\\n\\n[15] I've never liked the term \\\"deal flow,\\\" because it implies that the number of new startups at any given time is fixed. This is not only false, but it's the purpose of YC to falsify it, by causing startups to be founded that would not otherwise have existed.\\n\\n[16] She reports that they were all different shapes and sizes, because there was a run on air conditioners and she had to get whatever she could, but that they were all heavier than she could carry now.\\n\\n[17] Another problem with HN was a bizarre edge case that occurs when you both write essays and run a forum. When you run a forum, you're assumed to see if not every conversation, at least every conversation involving you. And when you write essays, people post highly imaginative misinterpretations of them on forums. Individually these two phenomena are tedious but bearable, but the combination is disastrous. You actually have to respond to the misinterpretations, because the assumption that you're present in the conversation means that not responding to any sufficiently upvoted misinterpretation reads as a tacit admission that it's correct. But that in turn encourages more; anyone who wants to pick a fight with you senses that now is their chance.\\n\\n[18] The worst thing about leaving YC was not working with Jessica anymore. We'd been working on YC almost the whole time we'd known each other, and we'd neither tried nor wanted to separate it from our personal lives, so leaving was like pulling up a deeply rooted tree.\\n\\n[19] One way to get more precise about the concept of invented vs discovered is to talk about space aliens. Any sufficiently advanced alien civilization would certainly know about the Pythagorean theorem, for example.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 72373, \"end_char_idx\": 74458, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
{"file_path":"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt","file_name":"paul_graham_essay.txt","file_type":"text\/plain","file_size":75042,"creation_date":"2024-09-03","last_modified_date":"2024-09-03","_node_content":"{\"id_\": \"536690a5-862d-432b-b9b6-6050dc22aba0\", \"embedding\": null, \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"f188e145-3081-44b3-adc6-fc272d34cc64\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"9e372d858ed00bec4d2517ec005e8e4031cc0a9ce54d207ca69a4ee70416c476\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"9af945f0-0da7-4825-8672-3a9e6bc81944\", \"node_type\": \"1\", \"metadata\": {\"file_path\": \"\/Users\/<USER>\/workspace\/xx\/llama_index_example\/..\/data\/paul_graham\/paul_graham_essay.txt\", \"file_name\": \"paul_graham_essay.txt\", \"file_type\": \"text\/plain\", \"file_size\": 75042, \"creation_date\": \"2024-09-03\", \"last_modified_date\": \"2024-09-03\"}, \"hash\": \"26c367ea2e364840fa0d487f3060046322d851b50ca4f5bd76b5a291df72e5b7\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"Individually these two phenomena are tedious but bearable, but the combination is disastrous. You actually have to respond to the misinterpretations, because the assumption that you're present in the conversation means that not responding to any sufficiently upvoted misinterpretation reads as a tacit admission that it's correct. But that in turn encourages more; anyone who wants to pick a fight with you senses that now is their chance.\\n\\n[18] The worst thing about leaving YC was not working with Jessica anymore. We'd been working on YC almost the whole time we'd known each other, and we'd neither tried nor wanted to separate it from our personal lives, so leaving was like pulling up a deeply rooted tree.\\n\\n[19] One way to get more precise about the concept of invented vs discovered is to talk about space aliens. Any sufficiently advanced alien civilization would certainly know about the Pythagorean theorem, for example. I believe, though with less certainty, that they would also know about the Lisp in McCarthy's 1960 paper.\\n\\nBut if so there's no reason to suppose that this is the limit of the language that might be known to them. Presumably aliens need numbers and errors and I\/O too. So it seems likely there exists at least one path out of McCarthy's Lisp along which discoveredness is preserved.\\n\\n\\n\\nThanks to Trevor Blackwell, John Collison, Patrick Collison, Daniel Gackle, Ralph Hazell, Jessica Livingston, Robert Morris, and Harj Taggar for reading drafts of this.\", \"mimetype\": \"text\/plain\", \"start_char_idx\": 73527, \"end_char_idx\": 75013, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"f188e145-3081-44b3-adc6-fc272d34cc64","doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64","ref_doc_id":"f188e145-3081-44b3-adc6-fc272d34cc64"}
