import heapq
from typing import List


class Solution:
    def findKthLargest(self, nums: List[int], k: int) -> int:
        heap = []
        for i in nums[:k]:
            heapq.heappush(heap, i)

        for i in nums[k:]:
            heapq.heappushpop(heap, i)

        return heap[0]

    def partion_solution(self, nums: List[int], k: int) -> int:
        pass


if __name__ == "__main__":
    solution = Solution()
    print(solution.findKthLargest([3, 2, 1, 5, 6, 4], 2))
    print(solution.findKthLargest([3, 2, 3, 1, 2, 4, 5, 5, 6], 4))
