N = 3  # 物品数量
W = 4  # 背包装载量
wt = [2, 1, 3]  # 物品重量
val = [4, 2, 3]  # 物品价值


def backpack():
    # init dp table
    # dp table 表示对于前i个物品，背包容量为j的情况下，可以装载货物的最大价值
    dp = [[0] * (W + 1) for _ in range(N + 1)]
    for i in range(N + 1):
        for j in range(W + 1):
            if j - wt[i - 1] < 0:
                # 背包容量不够，不能装载
                dp[i][j] = dp[i - 1][j]
            else:
                # 装入或者不装入，择优选择
                dp[i][j] = max(dp[i - 1][j], dp[i - 1][j - wt[i - 1]] + val[i - 1])
    return dp[N][W]


if __name__ == "__main__":
    print(backpack())
