def main(s):
    if not s:
        return 0
    dp = [0] * (len(s))
    stack = []
    for i in range(len(s)):
        if s[i] == "(":
            stack.append(i)
            dp[i] = dp[i - 1]
        else:
            if stack:
                left_index = stack.pop()
                dp[i] = i - left_index + 1 + dp[left_index]
            else:
                dp[i] = dp[i - 1]

    return max(dp)


if __name__ == "__main__":
    assert main(")()())") == 4
    assert main("()(()") == 2
