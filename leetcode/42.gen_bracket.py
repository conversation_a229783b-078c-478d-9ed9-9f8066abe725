def main(n):
    res = []

    def backtrack(answer, left, right):
        if len(answer) == 2 * n:
            res.append("".join(answer))
            print(answer)
            return
        if left < n:
            answer.append("(")
            backtrack(answer, left + 1, right)
            answer.pop()
        if right < left:
            answer.append(")")
            backtrack(answer, left, right + 1)
            answer.pop()

    backtrack([], 0, 0)
    return res


if __name__ == "__main__":
    print(main(3))
