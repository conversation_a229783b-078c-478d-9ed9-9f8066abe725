from typing import List


class Solution:
    def combinationSum(self, candidates: List[int], target: int) -> List[List[int]]:
        def backtrack(track, start, sums, target):
            if sum(track) > target:
                return
            if sum(track) == target:
                # item = track.sort()
                # if item not in res:
                res.append(track[::])
                return

            for i in range(start, len(candidates)):
                track.append(candidates[i])
                sums += candidates[i]
                backtrack(track, i, sums, target)
                sums -= candidates[i]
                track.pop()

        res = []
        backtrack([], 0, 0, target)
        return res


if __name__ == "__main__":
    solution = Solution()
    s = solution.combinationSum([2, 3, 6, 7], 7)
    print(s)
