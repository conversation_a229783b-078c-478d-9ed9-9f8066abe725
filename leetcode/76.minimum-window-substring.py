import collections


class Solution:
    def minWindow(self, s: str, t: str):
        need = collections.defaultdict(int)
        for i in t:
            need[i] += 1
        windows = collections.defaultdict(int)
        left, right = 0, 0
        valid = 0
        start = 0
        length = float("inf")

        while right < len(s):
            current_char = s[right]
            right += 1
            if current_char in need:
                windows[current_char] += 1
                if windows[current_char] == need[current_char]:
                    valid += 1

            while valid == len(need):
                if right - left < length:
                    start = left
                    length = right - left
                current_char = s[left]
                left += 1
                if current_char in need:
                    if windows[current_char] == need[current_char]:
                        valid -= 1
                    windows[current_char] -= 1

        return s[start : start + length] if length != float("inf") else ""

    # 这个解法最后一个测试用例超时了，应该是is_match太耗时
    def minWindow1(self, s: str, t: str) -> str:
        ret = ""
        left, right = 0, 0
        length = len(s)
        t_length = len(t)
        t_count = collections.defaultdict(int)
        for i in t:
            t_count[i] += 1

        while right < length:
            tmp_str = s[left : right + 1]
            if len(tmp_str) < t_length:
                right += 1
                continue

            if is_match(tmp_str, t_count):
                if ret and len(tmp_str) > len(ret):
                    left += 1
                else:
                    ret = tmp_str
                    left += 1
            else:
                right += 1

        return ret

    def test(self):
        "a unit test for minWindow"
        s = "ADOBECODEBANC"
        t = "ABC"
        print(self.minWindow(s, t))


def is_match(sub_str, t_count):
    current = collections.defaultdict(int)
    for i in sub_str:
        current[i] += 1

    for key, value in t_count.items():
        current_value = current[key]
        if not current_value:
            return False
        if current_value < value:
            return False

    return True


if __name__ == "__main__":
    solution = Solution()
    s = "ADOBECODEBANC"
    t = "ABC"
    s = "acbbaca"
    t = "aba"
    print(solution.minWindow(s, t))
