class Solution:
    def longestPalindrome(self, s: str) -> str:
        res = ""
        for i in range(len(s)):
            s1 = self.palindrome(s, i, i)
            s2 = self.palindrome(s, i, i + 1)
            print(i, s1, s2)

            res = res if len(res) > len(s1) else s1
            res = res if len(res) > len(s2) else s2
            print(res)

        return res

    def palindrome(self, s, left, right):
        while left >= 0 and right < len(s) and s[left] == s[right]:
            left -= 1
            right += 1

        return s[left + 1 : right]


if __name__ == "__main__":
    sol = Solution()
    print(sol.longestPalindrome("babad"))
