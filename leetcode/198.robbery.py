class Solution:
    def robber(self, nums):
        # 递归 +  备忘录
        def dp(start):
            if start >= n:
                return 0
            if memo[start]:
                return memo[start]
            res = max(dp(start + 1), nums[start] + dp(start + 2))
            memo[start] = res
            return res

        n = len(nums)
        memo = [None] * (n + 2)
        return dp(0)

    def robber1(self, nums):
        # dp 数组 倒序遍历
        n = len(nums)
        dp = [0] * (n + 2)
        for i in range(n - 1, -1, -1):
            dp[i] = max(dp[i + 1], nums[i] + dp[i + 2])

        return dp[0]

    def robber2(self, nums):
        # 压缩空间 使用常量级别的空间
        dp_i_2, dp_i_1, dp = 0, 0, 0
        n = len(nums)
        for i in range(n - 1, -1, -1):
            dp = max(dp_i_1, nums[i] + dp_i_2)
            dp_i_2 = dp_i_1
            dp_i_1 = dp

        return dp


if __name__ == "__main__":
    solution = Solution()
    print(solution.robber([1, 2, 3, 1]))
    print(solution.robber1([1, 2, 3, 1]))
    print(solution.robber2([1, 2, 3, 1]))
