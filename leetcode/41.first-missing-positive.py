from typing import List


class Solution:
    def firstMissingPositive(self, nums: List[int]) -> int:
        n = len(nums)

        for i in range(n):
            if nums[i] <= 0:
                nums[i] = n + 1

        for i in range(n):
            num = abs(nums[i])
            if num <= n:
                # nums[num-1] = -abs(nums[num-1])
                nums[num - 1] = -abs(nums[num - 1])
                # nums[num-1] = -1

        for i in range(n):
            if nums[i] > 0:
                return i + 1

        return n + 1

    def firstMissingPositive_2(self, nums: List[int]) -> int:
        n = len(nums)
        if 1 not in nums:
            return 1

        for i in range(n):
            if nums[i] <= 0 or nums[i] >= n:
                nums[i] = 1

        for i in range(n):
            num = abs(nums[i]) - 1
            nums[num] = -abs(nums[num])

        for i in range(n):
            if nums[i] > 0:
                return i + 1

        return n + 1


if __name__ == "__main__":
    solution = Solution()
    print(solution.firstMissingPositive([2, 2]))
    print(solution.firstMissingPositive([7, 8, 9, 11, 12]))
    print(solution.firstMissingPositive([3, 4, -1, 1]))

    print(solution.firstMissingPositive_2([2, 2]))
    print(solution.firstMissingPositive_2([7, 8, 9, 11, 12]))
    print(solution.firstMissingPositive_2([3, 4, -1, 1]))
