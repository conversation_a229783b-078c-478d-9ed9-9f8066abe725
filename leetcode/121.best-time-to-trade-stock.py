class Solution:
    def max_profit(self, prices):
        # dp
        # dp[i][0] 表示第i天不持有股票时的收益
        # dp[i][1] 表示第i天持有股票时的收益
        n = len(prices)
        dp = [[0, 0]] * n
        dp[0][0] = 0
        dp[0][1] = -prices[0]
        for i in range(1, n):
            dp[i][0] = max(dp[i - 1][0], dp[i - 1][1] + prices[i])
            dp[i][1] = max(dp[i - 1][1], -prices[i])

        return dp[n - 1][0]

    def iter_max_profit(self, prices):
        res = 0
        min_price = float("inf")
        for price in prices:
            res = max(res, price - min_price)
            min_price = min(price, min_price)
        return res


if __name__ == "__main__":
    solution = Solution()
    assert solution.max_profit([7, 1, 5, 3, 6, 4]) == 5
    assert solution.max_profit([7, 6, 4, 3, 1]) == 0
    assert solution.iter_max_profit([7, 1, 5, 3, 6, 4]) == 5
    assert solution.iter_max_profit([7, 6, 4, 3, 1]) == 0
