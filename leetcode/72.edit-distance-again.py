class Solution:
    def edit_distance(self, word1, word2):
        # dp数组 从左上角到右下角遍历
        m = len(word1)
        n = len(word2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        for i in range(m + 1):
            dp[i][0] = i

        for i in range(n + 1):
            dp[0][i] = i

        for i in range(m + 1):
            for j in range(n + 1):
                if word1[i - 1] == word2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1]
                else:
                    dp[i][j] = min(
                        dp[i][j - 1] + 1,
                        dp[i - 1][j] + 1,
                        dp[i - 1][j - 1] + 1,
                    )

        return dp[m][n]

    def edit_distance1(self, word1, word2):
        def dp(m, n):
            if (m, n) in memo:
                return memo[(m, n)]
            if m == 0:
                return n + 1
            if n == 0:
                return m + 1

            if word1[m] == word2[n]:
                memo[(m, n)] = dp(m - 1, n - 1)
            else:
                memo[(m, n)] = min(
                    dp(m - 1, n - 1) + 1,
                    dp(m - 1, n) + 1,
                    dp(m, n - 1) + 1,
                )

            return memo[(m, n)]

        memo = {}
        return dp(len(word1) - 1, len(word2) - 1)


if __name__ == "__main__":
    solution = Solution()
    print(solution.edit_distance("sea", "ate"))
    print(solution.edit_distance1("sea", "ate"))

    print(solution.edit_distance("horse", "ros"))
    print(solution.edit_distance1("horse", "ros"))
