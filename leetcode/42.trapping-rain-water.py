from typing import List


class Solution:
    def trap(self, height: List[int]) -> int:
        # 暴力解法  超时
        length = len(height)
        res = 0
        for i in range(length):
            l_max, r_max = 0, 0
            for j in range(i + 1):
                l_max = max(l_max, height[j])
            for j in range(i, length):
                r_max = max(r_max, height[j])
            item = min(l_max, r_max) - height[i]
            res += item
        return res

    def trap2(self, height: List[int]) -> int:
        length = len(height)
        res = 0
        l_max = [0] * length
        r_max = [0] * length

        l_max[0] = height[0]
        r_max[length - 1] = height[length - 1]

        for i in range(1, length):
            l_max[i] = max(l_max[i - 1], height[i])

        for i in range(length - 2, -1, -1):
            r_max[i] = max(r_max[i + 1], height[i])

        for i in range(length):
            res += min(l_max[i], r_max[i]) - height[i]

        return res

    def trap3(self, height: List[int]) -> int:
        res = 0
        n = len(height)
        left, right = 0, n - 1
        l_max = height[left]
        r_max = height[right]
        while left < right:
            l_max = max(l_max, height[left])
            r_max = max(r_max, height[right])

            if l_max < r_max:
                res += l_max - height[left]
                left += 1
            else:
                res += r_max - height[right]
                right -= 1

        return res


if __name__ == "__main__":
    solution = Solution()
    print(solution.trap([0, 1, 0, 2, 1, 0, 1, 3, 2, 1, 2, 1]))
    print(solution.trap2([0, 1, 0, 2, 1, 0, 1, 3, 2, 1, 2, 1]))
    print(solution.trap3([0, 1, 0, 2, 1, 0, 1, 3, 2, 1, 2, 1]))
    print(solution.trap([4, 2, 0, 3, 2, 5]))
    print(solution.trap2([4, 2, 0, 3, 2, 5]))
    print(solution.trap3([4, 2, 0, 3, 2, 5]))
