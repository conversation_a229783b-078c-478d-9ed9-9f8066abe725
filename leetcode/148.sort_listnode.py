from LinkedList.ListNode import ListNode


class Solution:
    def sortListnode(self, head):
        def merge(heada, headb):
            dummy = ListNode(0)
            tmp, a, b = dummy, heada, headb

            while a and b:
                if a.val > b.val:
                    tmp.next = b
                    b = b.next
                else:
                    tmp.next = a
                    a = a.next
                tmp = tmp.next

            if a:
                tmp.next = a
            if b:
                tmp.next = b
            return dummy.next

        def sort(head, tail):
            if head == tail:
                return head
            if head.next == tail:
                head.next = None
                return head
            fast, slow = head, head
            while fast != tail:
                fast = fast.next
                slow = slow.next
                if fast:
                    fast = fast.next
            mid = slow
            return merge(sort(head, mid), sort(mid, tail))

        return sort(head, None)


if __name__ == "__main__":
    head = ListNode.gen_from_list([1, 2, 3, 4, 5, 4, 3, 2])
    solution = Solution()
    print(solution.sortListnode(head))
