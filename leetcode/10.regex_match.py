class Solution:
    def isMatch(self, s: str, p: str) -> bool:
        def dp(i, j):
            m = len(s)
            n = len(p)
            if i == m:
                if (n - j) % 2 == 1:
                    return False
                for a in range(j, n - 1, 2):
                    if p[a + 1] != "*":
                        return False
                return True
            if j == n:
                return i == m
            if (i, j) in memo:
                return memo[(i, j)]
            res = False
            if s[i] == p[j] or p[j] == ".":
                if j < n - 1 and p[j + 1] == "*":
                    res = dp(i, j + 2) or dp(i + 1, j)
                else:
                    res = dp(i + 1, j + 1)
            else:
                if j < n - 1 and p[j + 1] == "*":
                    res = dp(i, j + 2)
                else:
                    res = False
            memo[(i, j)] = res
            return res

        memo = {}
        return dp(0, 0)

    def is_match_dp_table(self, s, p):
        m, n = len(s), len(p)

        def matches(i: int, j: int) -> bool:
            if i == 0:
                return False
            if p[j - 1] == ".":
                return True
            return s[i - 1] == p[j - 1]

        f = [[False] * (n + 1) for _ in range(m + 1)]
        f[0][0] = True
        for i in range(m + 1):
            for j in range(1, n + 1):
                if p[j - 1] == "*":
                    f[i][j] |= f[i][j - 2]
                    if matches(i, j - 1):
                        f[i][j] |= f[i - 1][j]
                else:
                    if matches(i, j):
                        f[i][j] |= f[i - 1][j - 1]
        return f[m][n]


if __name__ == "__main__":
    solution = Solution()
    # print(solution.isMatch('mississippi', 'mis*is*p*.'))
    print(solution.is_match_dp_table("aa", "a*"))
    # print(solution.isMatch('a', '.*..'))
    # print(solution.isMatch('a', '.*..'))
    # print(solution.isMatch('bb', '.bab'))
    # print(solution.isMatch('ab', '.*..'))
    # print(solution.isMatch('aa', 'a'))
    # print(solution.isMatch('aa', 'a*'))
    # print(solution.isMatch('ab', '.*'))
