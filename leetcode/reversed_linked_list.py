class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

    def __repr__(self):
        return "Node({})".format(str(self.val))


class Solution:
    def reverseList(self, head: ListNode) -> ListNode:
        if head is None or head.next is None:
            return head
        current = self.reverseList(head.next)
        head.next.next = head
        head.next = None
        return current


if __name__ == "__main__":
    s = Solution()
    test = ListNode(1)
    test2 = ListNode(2)
    test3 = ListNode(3)
    test4 = ListNode(4)
    test5 = ListNode(5)
    test.next = test2
    test2.next = test3
    test3.next = test4
    test4.next = test5
    print(s.reverseList(test))
