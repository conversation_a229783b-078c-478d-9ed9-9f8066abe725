### 相同的树
**难度: Medium**

```
给定两个以字符串形式表示的非负整数 num1 和 num2，返回 num1 和 num2 的乘积，它们的乘积也表示为字符串形式。

```
示例
```
输入: num1 = "2", num2 = "3"
输出: "6"
```
```
输入: num1 = "123", num2 = "456"
输出: "56088"
```
说明：

num1 和 num2 的长度小于110。

num1 和 num2 只包含数字 0-9。

num1 和 num2 均不以零开头，除非是数字 0 本身。

不能使用任何标准库的大数类型（比如 BigInteger）或直接将输入转换为整数来处理。

答案
```python

class Solution:
    def multiply(self, num1, num2):
        """
        :type num1: str
        :type num2: str
        :rtype: str
        """
        d = {'0':0,'1':1,'2':2,'3':3,'4':4,'5':5,'6':6,'7':7,'8':8,'9':9}
        if num1 == '0' or num2 == '0':
            return '0'
        num1,num2 = num1[::-1],num2[::-1]
        tmp_res = []
        tmp_res = [0 for i in range(len(num1) + len(num2))]
        for i in range(len(num1)):
            for j in range(len(num2)):
                tmp_res[i+j] += d[num1[i]] * d[num2[j]]

        res = [0 for i in range(len(num1) + len(num2))]
        for i in range(len(num1) + len(num2)):
            res[i] = tmp_res[i] % 10
            if i < len(num1) + len(num2)-1:
                tmp_res[i+1] += tmp_res[i] // 10
        return ''.join(str(i) for i in res[::-1]).lstrip('0')

```
