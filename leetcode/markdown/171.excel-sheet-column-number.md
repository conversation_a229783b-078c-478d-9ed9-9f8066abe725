### 搜索插入位置
**难度: Easy**

```
给定一个Excel表格中的列名称，返回其相应的列序号。
例如：
    A -> 1
    B -> 2
    C -> 3
    ...
    Z -> 26
    AA -> 27
    AB -> 28
    ...

```
示例
```
输入: "A"
输出: 1
```

答案
```python
class Solution:
    def titleToNumber(self, s):
        """
        :type s: str
        :rtype: int
        """
        maps = {}
        for i in range(65,91):
            maps[chr(i)] = i - 64
        nums = list(s)
        nums.reverse()
        num = 0
        for index,item in enumerate(nums):
            num += maps[item] * (26**index)
        return num

```
