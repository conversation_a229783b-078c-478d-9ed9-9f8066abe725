### 回文数
**难度: Easy**

```
判断一个整数是否是回文数。回文数是指正序（从左向右）和倒序（从右向左）读都是一样的整数。

```
示例
```
输入: 121
输出: true
```
```
输入: -121
输出: false
解释: 从左向右读, 为 -121 。 从右向左读, 为 121- 。因此它不是一个回文数。
```
```
输入: 10
输出: false
解释: 从右向左读, 为 01 。因此它不是一个回文数。
```
答案
```python
class Solution:
    def isPalindrome(self, x):
        """
        :type x: int
        :rtype: bool
        """
        li = [i for i in str(x)]
        flag = True
        while len(li) >1 and flag:
            fisrt = li[0]
            last = li.pop()
            if fisrt != last:
                flag =  False
            del li[0]
        return flag
```
```python
class Solution:
    def isPalindrome(self, x):
        """
        :type x: int
        :rtype: bool
        """
        x = str(x)
        return x == x[::-1]
```
