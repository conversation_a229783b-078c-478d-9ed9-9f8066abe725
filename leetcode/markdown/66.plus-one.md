### 加一
**难度: Easy**

```
给定一个由整数组成的非空数组所表示的非负整数，在该数的基础上加一。

最高位数字存放在数组的首位， 数组中每个元素只存储一个数字。

你可以假设除了整数 0 之外，这个整数不会以零开头。

```
示例
```
输入: [1,2,3]
输出: [1,2,4]
解释: 输入数组表示数字 123。
```
```
输入: [4,3,2,1]
输出: [4,3,2,2]
解释: 输入数组表示数字 4321。
```

答案
```python
class Solution:
    def plusOne(self, digits):
        """
        :type digits: List[int]
        :rtype: List[int]
        """
        f = 1
        res = []
        for i in digits[::-1]:
            r = i + f
            if r > 9:
                f = 1
                r = r - 10
            else:
                f = 0
            res.append(r)
        if f == 1:
            res.append(1)
        return res[::-1]

```
