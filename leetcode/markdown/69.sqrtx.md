### x 的平方根
**难度: Easy**

实现 `int sqrt(int x)` 函数。

计算并返回 x 的平方根，其中 x 是非负整数。

由于返回类型是整数，结果只保留整数的部分，小数部分将被舍去。
示例
```
输入: 8
输出: 2
说明: 8 的平方根是 2.82842...,
     由于返回类型是整数，小数部分将被舍去。
```
答案
```python
class Solution:
    def mySqrt(self, x):
        """
        :type x: int
        :rtype: int
        """
        if x == 0:
            return 0
        if x == 1:
            return 1
        l,r = 0,x-1
        while l <= r:
            mid = (l + r) //2
            if mid * mid -x == 0:
                return mid
            elif mid * mid -x > 0:
                r = mid - 1
            else:
                l = mid + 1
        return r

```
