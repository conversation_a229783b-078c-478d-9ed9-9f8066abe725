### 搜索插入位置
**难度: Easy**

```
给定一个只包括 '('，')'，'{'，'}'，'['，']' 的字符串，判断字符串是否有效。

有效字符串需满足：

左括号必须用相同类型的右括号闭合。
左括号必须以正确的顺序闭合。
注意空字符串可被认为是有效字符串。

```
示例
```
输入: "()"
输出: true
```
```
输入: "()[]{}"
输出: true
```
```
输入: "([)]"
输出: false
```
答案
```python
class Solution:
    def isValid(self, s):
        """
        :type s: str
        :rtype: bool
        """
        left = '([{'
        right = ')]}'
        stack = []
        for char in s:
            if char in left:
                stack.append(char)
            if char in right:
                if not stack:
                    return False
                tmp = stack.pop()
                if tmp == '(' and char != ')':
                    return False
                if tmp == '[' and char != ']':
                    return False
                if tmp == '{' and char != '}':
                    return False
        return stack == []
```
