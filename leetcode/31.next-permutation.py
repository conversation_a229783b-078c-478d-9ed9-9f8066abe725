from typing import List


class Sloution:
    def nextPermutation(self, nums: List[int]) -> None:
        """
        Do not return anything, modify nums in-place instead.
        """
        # 寻找i 使得 a[i] < a[i+1]
        i = len(nums) - 2
        while i >= 0 and nums[i] >= nums[i + 1]:
            i -= 1

        if i >= 0:
            j = len(nums) - 1
            # 从后向前 寻找j 使得 a[i] < a[j]
            while j >= 0 and nums[i] >= nums[j]:
                j -= 1

            nums[i], nums[j] = nums[j], nums[i]

        left, right = i + 1, len(nums) - 1
        while left < right:
            nums[left], nums[right] = nums[right], nums[left]
            left += 1
            right -= 1


if __name__ == "__main__":
    solution = Sloution()

    nums = [5, 1, 1]
    solution.nextPermutation(nums)
    assert nums == [1, 1, 5]

    nums = [1, 5, 1]
    solution.nextPermutation(nums)
    assert nums == [5, 1, 1]

    nums = [1, 1, 5]
    solution.nextPermutation(nums)
    assert nums == [1, 5, 1]

    nums = [1, 2]
    solution.nextPermutation(nums)
    assert nums == [2, 1]
