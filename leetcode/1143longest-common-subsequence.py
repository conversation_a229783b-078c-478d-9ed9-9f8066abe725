class Solution:
    def longestCommonSubsequence(self, text1: str, text2: str) -> int:
        m, n = len(text1), len(text2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if text1[i - 1] == text2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                else:
                    dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])

        return dp[m][n]

    def longestCommonSubsequence1(self, text1: str, text2: str) -> int:
        width = len(text1)
        height = len(text2)

        dp = [[0] * (height + 1) for _ in range(width + 1)]

        for i in range(1, width):
            for j in range(1, height):
                if text1[i - 1] == text2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                else:
                    dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])

        return dp[width][height]


if __name__ == "__main__":
    sol = Solution()
    text1 = "abcde"
    text2 = "ace"
    print(sol.longestCommonSubsequence(text1, text2))
