from typing import List


class Solution:
    @staticmethod
    def permute(nums: List[int]) -> List[List[int]]:
        def backtrack(nums, track):
            if len(track) == len(nums):
                res.append(track[:])
                return res

            for num in nums:
                if num in track:
                    continue
                track.append(num)
                backtrack(nums, track)
                del track[-1]

        res = []
        track = []
        backtrack(nums, track)
        return res


if __name__ == "__main__":
    s = Solution()
    print(s.permute([1, 2, 3]))
