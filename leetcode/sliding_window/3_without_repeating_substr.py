import collections


class Solution:
    def lengthOfLongestSubstring(self, s: str) -> int:
        res = 0
        left, right = 0, 0
        windows = collections.defaultdict(int)
        while right < len(s):
            char = s[right]
            right += 1
            windows[char] += 1

            if windows[char] > 1:
                left_char = windows[left]
                left += 1
                windows[left_char] -= 1

            res = max(res, right - left)
        return res


if __name__ == "__main__":
    solution = Solution()
    string = "abcabcbb"
    print(solution.lengthOfLongestSubstring(string))
