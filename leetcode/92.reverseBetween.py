from Algorithm.LinkedList.ListNode import ListNode


class Solution:
    def __init__(self):
        self.tmp = None

    def reverseBetween(self, head: ListNode, left: int, right: int) -> ListNode:
        dummy = ListNode(-1, head)
        prev = dummy
        cur = head
        for _ in range(left - 1):
            prev = prev.next
            cur = cur.next

        for _ in range(right - left):
            tmp = cur.next
            cur.next = tmp.next
            tmp.next = prev.next
            prev.next = tmp

        return dummy.next

    def recursive(self, head, left, right):
        if left == 1:
            return self.reverseN(head, right)
        head.next = self.recursive(head.next, left - 1, right - 1)
        return head

    def reverseN(self, head, n):
        if n == 1:
            self.tmp = head.next
            return head

        last = self.reverseN(head.next, n - 1)
        head.next.next = head
        head.next = self.tmp
        return last


if __name__ == "__main__":
    left, right = 2, 4
    head = ListNode.gen_from_list([1, 2, 3, 4, 5])
    solution = Solution()
    print(head)
    print(solution.reverseBetween(head, left, right))
    print(solution.recursive(head, left, right))
