from typing import List


class Solution:
    def maximalSquare(self, matrix: List[List[str]]) -> int:
        m = len(matrix)
        n = len(matrix[0])

        dp = [[0] * n for _ in range(m)]

        for i in range(m):
            dp[i][0] = int(matrix[i][0])

        for i in range(n):
            dp[0][i] = int(matrix[0][i])

        for i in range(1, m):
            for j in range(1, n):
                if matrix[i][j] == "0":
                    continue
                else:
                    dp[i][j] = min(dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1]) + 1

        print(dp)
        max_len = max([max(dp[i]) for i in range(m)])

        return max_len**2


if __name__ == "__main__":
    solution = Solution()
    s = [
        ["1", "0", "1", "0", "0"],
        ["1", "0", "1", "1", "1"],
        ["1", "1", "1", "1", "1"],
        ["1", "0", "0", "1", "0"],
    ]
    print(solution.maximalSquare(s))
