# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next
from typing import Optional

from Algorithm.LinkedList.ListNode import ListNode


class Solution:
    def reverseKGroup(self, head: Optional[ListNode], k: int) -> Optional[ListNode]:
        if not head:
            return None
        left = head
        right = head
        for i in range(k):
            if not right:
                return head
            right = right.next

        new_head = self.reverse(left, right)
        left.next = self.reverseKGroup(right, k)
        return new_head

    def reverse(self, left, right):
        pre = None
        cur = left
        while cur != right:
            next_node = cur.next
            cur.next = pre
            pre = cur
            cur = next_node

        return pre


if __name__ == "__main__":
    solution = Solution()
    head = ListNode.gen_from_list([1, 2, 3, 4, 5])
    k = 2
    print(head)
    print(solution.reverseKGroup(head, k))

    # head = ListNode.gen_from_list([1, 2, 3, 4, 5])
    # k = 3
    # print(head)
    # print(solution.reverseKGroup(head, k))
