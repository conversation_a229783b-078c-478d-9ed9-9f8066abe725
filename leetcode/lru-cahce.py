class DLinkNode:
    def __init__(self, key=0, val=0):
        self.key = key
        self.val = val
        self.prev = None
        self.next = None


class LRUCache:
    def __init__(self, capacity: int):
        self.cache = {}
        self.head = DLinkNode()
        self.tail = DLinkNode()
        self.head.next = self.tail
        self.tail.prev = self.head
        self.capacity = capacity
        self.size = 0

    def get(self, key: int) -> int:
        # exist return and movetohead
        # not exist return -1
        node = self.cache.get(key)
        if not node:
            return -1
        self.move_node_to_head(node)
        return node.val

    def put(self, key: int, value: int) -> None:
        # if exist,  update and movetohead
        # if not exist,  addtohead
        #   if size > capacity, remove tail
        node = self.cache.get(key)
        if node:
            node.val = value
            self.move_node_to_head(node)
        else:
            node = DLinkNode(key, value)
            self.cache[key] = node
            self.add_node_to_head(node)
            self.size += 1
            if self.size > self.capacity:
                remove_node = self.remove_tail()
                self.cache.pop(remove_node.key)
                self.size -= 1

    def move_node_to_head(self, node):
        self.remove_node(node)
        self.add_node_to_head(node)

    def remove_tail(self):
        last_node = self.tail.prev
        self.remove_node(last_node)
        return last_node

    def add_node_to_head(self, node):
        first_node = self.head.next
        first_node.prev = node
        node.next = first_node
        self.head.next = node
        node.prev = self.head

    def remove_node(self, node):
        node.prev.next = node.next
        node.next.prev = node.prev


if __name__ == "__main__":
    #     ["LRUCache","put","put","get","put","get","put","get","get","get"]
    # [[2],[1,1],[2,2],[1],[3,3],[2],[4,4],[1],[3],[4]]
    # [null,null,null,1,null,-1,null,-1,3,4]
    cache = LRUCache(2)
    cache.put(1, 1)
    cache.put(2, 2)
    print(cache.get(1))
    cache.put(3, 3)
    print(cache.get(2))
    cache.put(4, 4)
    print(cache.get(1))
    print(cache.get(3))
    print(cache.get(4))
