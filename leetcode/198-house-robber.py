from typing import List


class Solution:
    def rob(self, nums: List[int]) -> int:
        dp_i, dp_i_1, dp_i_2 = 0, 0, 0
        n = len(nums)
        for i in range(n - 1, -1, -1):
            dp_i = max(dp_i_1, dp_i_2 + nums[i])
            dp_i_2 = dp_i_1
            dp_i_1 = dp_i

        return dp_i

    def rob2(self, nums: List[int]) -> int:
        n = len(nums)
        dp = [0] * (n + 2)
        for i in range(n - 1, -1, -1):
            dp[i] = max(dp[i + 1], dp[i + 2] + nums[i])
        return dp[0]

    def rob1(self, nums: List[int]) -> int:
        def dp(sub_nums, start):
            if start >= len(sub_nums):
                return 0

            if memo[start] is not None:
                return memo[start]

            res = max(
                dp(sub_nums, start + 1), sub_nums[start] + dp(sub_nums, start + 2)
            )
            memo[start] = res

            return res

        memo = [None] * len(nums)

        return dp(nums, 0)


if __name__ == "__main__":
    s = Solution()
    print(s.rob([1, 2, 3, 1]))
    print(s.rob1([1, 2, 3, 1]))
    print(s.rob2([1, 2, 3, 1]))
