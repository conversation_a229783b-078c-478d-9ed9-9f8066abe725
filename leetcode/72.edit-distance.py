class Solution:
    def minDistance(self, word1: str, word2: str) -> int:
        m = len(word1)
        n = len(word2)
        # init dp table and bad case table
        dp = [[0] * (n + 1) for i in range(m + 1)]
        for i in range(n + 1):
            dp[0][i] = i
        for i in range(m + 1):
            dp[i][0] = i

        for i in range(1, m + 1):
            # state transition function
            for j in range(1, n + 1):
                if word1[i - 1] == word2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1]
                else:
                    dp[i][j] = min(
                        dp[i][j - 1] + 1,
                        dp[i - 1][j] + 1,
                        dp[i - 1][j - 1] + 1,
                    )

        return dp[m][n]

    def memo_solution(self, word1, word2):
        memo = {}

        def dp(i, j):
            if (i, j) in memo:
                return memo[(i, j)]
            if i == -1:
                return j + 1
            if j == -1:
                return i + 1

            if word1[i] == word2[j]:
                memo[(i, j)] = dp(i - 1, j - 1)
            else:
                memo[(i, j)] = min(
                    dp(i, j - 1) + 1,
                    dp(i - 1, j) + 1,
                    dp(i - 1, j - 1) + 1,
                )
            return memo[(i, j)]

        return dp(len(word1) - 1, len(word2) - 1)


if __name__ == "__main__":
    solu = Solution()
    # print(solu.minDistance("horse", "ros"))
    print(solu.minDistance("sea", "ate"))
    print(solu.memo_solution("sea", "ate"))
