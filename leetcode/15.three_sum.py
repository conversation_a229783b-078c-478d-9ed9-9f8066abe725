class Solution:
    def threeSum(self, nums):
        res = []
        n = len(nums)
        nums.sort()

        for first in range(n):
            if first > 0 and nums[first] == nums[first - 1]:
                continue

            third = n - 1
            target = -nums[first]
            for second in range(first + 1, n):
                if second > first + 1 and nums[second] == nums[second - 1]:
                    continue

                while second < third and nums[second] + nums[third] > target:
                    third -= 1

                if second == third:
                    break

                if nums[second] + nums[third] == target:
                    res.append([nums[first], nums[second], nums[third]])

        return res


if __name__ == "__main__":
    solution = Solution()
    print(solution.threeSum([-1, 0, 1, 2, -1, -4]))
    print(solution.threeSum([]))
    print(solution.threeSum([0]))
    print(solution.threeSum([0, 0, 0]))
