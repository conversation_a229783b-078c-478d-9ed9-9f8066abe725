from typing import List


class Solution:
    def twoSum(self, nums: List[int], target: int) -> List[int]:
        nums_map = {num: index for index, num in enumerate(nums)}

        for index, i in enumerate(nums):
            last = target - i
            if last != i and last in nums_map:
                return [index, nums_map[last]]


if __name__ == "__main__":
    solution = Solution()
    print(solution.twoSum([2, 7, 11, 15], 9))
    print(solution.twoSum([3, 2, 4], 6))
