from typing import List

from utils import time_wrapper


class Solution:
    @time_wrapper
    def lengthOfLongestSubstring(self, s: str) -> int:
        if not s:
            return 0
        res = 1

        tmp = []
        for i in s:
            if i not in tmp:
                tmp.append(i)
                res = max(res, len(tmp))

            else:
                pos = tmp.index(i)
                tmp = tmp[pos + 1 :]
                if i not in tmp:
                    tmp.append(i)

                res = max(res, len(tmp))

        res = max(res, len(tmp))

        return res

    @time_wrapper
    def lengthOfLongestSubstring1(self, s: str) -> int:
        if not s:
            return 0
        res = 1
        tmp = ''
        for i in s:
            if i not in tmp:
                tmp += i
                res = max(res, len(tmp))

            else:
                pos = tmp.index(i)
                tmp = tmp[pos + 1 :]
                if i not in tmp:
                    tmp += i

                res = max(res, len(tmp))

        res = max(res, len(tmp))

        return res

    def lengthOfLongestSubstring3(self, s: str) -> int:
        n = len(s)
        if n == 0:
            return 0

        # 使用字典记录字符出现的位置
        char_index = {}
        left = 0
        max_len = 0

        for right in range(n):
            # 如果当前字符在字典中,更新左指针
            if s[right] in char_index:
                left = max(left, char_index[s[right]] + 1)

            # 更新字符在字典中的位置
            char_index[s[right]] = right

            # 更新最长子串长度
            max_len = max(max_len, right - left + 1)

        return max_len

    def lengthOfLongestSubstring2(self, s: str) -> int:
        n = len(s)
        if n == 0:
            return 0

        # 使用掩码记录字符是否出现过
        mask = 0
        left = 0
        max_len = 0

        for right in range(n):
            # 获取当前字符的 Unicode 码点
            curr_char = ord(s[right])

            # 如果当前字符在掩码中,更新左指针
            if (mask & (1 << curr_char)) != 0:
                while (mask & (1 << curr_char)) != 0:
                    prev_char = ord(s[left])
                    mask &= ~(1 << prev_char)
                    left += 1
            else:
                # 将当前字符加入掩码
                mask |= 1 << curr_char
                max_len = max(max_len, right - left + 1)

        return max_len


if __name__ == "__main__":
    solution = Solution()
    assert solution.lengthOfLongestSubstring2("ggububgvfk") == 6
    assert solution.lengthOfLongestSubstring2("dvdf") == 3
    assert solution.lengthOfLongestSubstring2("aabaab!bb") == 3
    assert solution.lengthOfLongestSubstring2("abcabcbb") == 3
    assert solution.lengthOfLongestSubstring2("bbbbb") == 1
    assert solution.lengthOfLongestSubstring2("pwwkew") == 3
    assert solution.lengthOfLongestSubstring2("uqinntq") == 4
