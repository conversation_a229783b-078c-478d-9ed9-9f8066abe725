import os
from PIL import Image
import torch
from transformers import CLIPProcessor, CLIPModel
from tqdm import tqdm


def load_model():
    model = CLIPModel.from_pretrained("jinaai/jina-clip-v2")
    processor = CLIPProcessor.from_pretrained("jinaai/jina-clip-v2")
    # model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
    # processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
    return model, processor


def get_image_embeddings(model, processor, image_paths):
    embeddings = []
    images = []

    for img_path in tqdm(image_paths, desc="Processing images"):
        try:
            image = Image.open(img_path)
            inputs = processor(images=image, return_tensors="pt", padding=True)
            image_features = model.get_image_features(**inputs)
            embeddings.append(image_features)
            images.append(img_path)
        except Exception as e:
            print(f"Error processing {img_path}: {e}")
            continue

    return torch.cat(embeddings), images


def get_text_embedding(model, processor, text):
    inputs = processor(text=text, return_tensors="pt", padding=True)
    text_features = model.get_text_features(**inputs)
    return text_features


def find_similar_images(image_embeddings, text_embedding, images, top_k=5):
    # Normalize embeddings
    image_embeddings = image_embeddings / image_embeddings.norm(dim=-1, keepdim=True)
    text_embedding = text_embedding / text_embedding.norm(dim=-1, keepdim=True)

    # Calculate similarity scores
    similarity = torch.mm(text_embedding, image_embeddings.T)

    # Get top-k similar images
    values, indices = similarity[0].topk(min(top_k, len(images)))

    results = []
    for score, idx in zip(values, indices):
        results.append({'image_path': images[idx], 'similarity_score': score.item()})

    return results


def main():
    try:
        # Load model
        print("Loading model...")
        model, processor = load_model()
        model = model.to('cuda' if torch.cuda.is_available() else 'cpu')
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Using device: {device}")

        # Get all images from esg_charts directory
        image_dir = "esg_charts"
        if not os.path.exists(image_dir):
            print(f"Directory {image_dir} does not exist!")
            return

        image_paths = []
        for filename in os.listdir(image_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_paths.append(os.path.join(image_dir, filename))

        print(f"Found {len(image_paths)} images in {image_dir}")

        if not image_paths:
            print("No images found in the directory!")
            return

        # Get image embeddings
        print("Processing images...")
        image_embeddings, images = get_image_embeddings(model, processor, image_paths)
        print(f"Generated embeddings shape: {image_embeddings.shape}")

        # Get text embedding for "scope3"
        print("Generating text embedding...")
        text_query = "scope3 carbon emissions sustainability"
        text_embedding = get_text_embedding(model, processor, text_query)
        print(f"Text embedding shape: {text_embedding.shape}")

        # Find similar images
        print("Finding similar images...")
        results = find_similar_images(image_embeddings, text_embedding, images)

        # Print results
        print("\nTop similar images for 'scope3':")
        for idx, result in enumerate(results, 1):
            print(f"{idx}. {result['image_path']} (Similarity: {result['similarity_score']:.4f})")

    except Exception as e:
        print(f"An error occurred: {str(e)}")


if __name__ == "__main__":
    main()
