import paddle
from paddlenlp.embeddings import TokenEmbedding, list_embedding_name


def main():
    paddle.set_device("cpu")

    # 查看预训练embedding名称：
    print(list_embedding_name())  # ['w2v.baidu_encyclopedia.target.word-word.dim300']

    # 初始化TokenEmbedding， 预训练embedding没下载时会自动下载并加载数据
    token_embedding = TokenEmbedding(embedding_name="w2v.baidu_encyclopedia.target.word-word.dim300")

    print(token_embedding.search("中国"))
    print(token_embedding.search("美国"))
    score = token_embedding.cosine_sim("比例", "行权比例")
    print(score)  # 0.49586025

    # vision(['中国', '美国'], token_embedding)
    # vision([], token_embedding)


def vision(labels, token_embedding):
    # 获取词表中前1000个单词
    labels = labels or token_embedding.vocab.to_tokens(list(range(0, 1000)))
    test_token_embedding = token_embedding.search(labels)

    # 引入VisualDL的LogWriter记录日志
    from visualdl import LogWriter

    with LogWriter(logdir="./visualize") as writer:
        writer.add_embeddings(tag="test", mat=test_token_embedding, metadata=labels)


if __name__ == "__main__":
    main()
