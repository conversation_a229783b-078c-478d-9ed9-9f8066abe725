from typing import List
import promptulate as pne
from promptulate import BaseLLM, ChatOpenAI, MessageSet, AssistantMessage, SystemMessage
from promptulate.llms import OpenAI
from pydantic import BaseModel, Field
import os

os.environ["OPENAI_BASE_URL"] = ""
os.environ["OPENAI_API_KEY"] = "sk-"
# os.environ["OPENAI_BASE_URL"] = "https://api.deepseek.com"
# os.environ["DEEPSEEK_API_KEY"] = "sk-"
# os.environ["OPENAI_API_KEY"] = "sk-"


# class LLMResponse(BaseModel):
#     provinces: List[str] = Field(description="List of provinces' names")


# llm = ChatOpenAI(base_url="https://oneapi.cheftin.com/v1")
# llm = ChatOpenAI(base_url="https://oneapi.cheftin.com/v1/chat/completions")
#
#
# response = pne.chat(
#     messages="Please tell me all provinces in China.",
#     output_schema=LLMResponse,
#     custom_llm=llm,
# )
# # messages = MessageSet([
# #     SystemMessage(content="Please tell me all provinces in China.")
# # ])
# # response = llm.predict(messages, output_schema=LLMResponse)
# # response: str = pne.chat(model="ollama/llama3",  messages="Please tell me all provinces in China.",utput_schema=LLMResponse)
# print(response)


# resp: str = pne.chat(model="ollama/llama3", messages=[{"content": "Hello, how are you?", "role": "user"}])
# print(resp)
# resp: str = pne.chat(model="ollama/llama3", messages=[{"content": "Please tell me all provinces in China.", "role": "user"}])
# print(resp)


from transformers import AutoModel, AutoTokenizer

model_name = "BUAADreamer/Chinese-LLaVA-Med-7B"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModel.from_pretrained(model_name)
