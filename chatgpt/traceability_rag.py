# !pip install unstructured-client unstructured[pdf] langchain chromadb huggingface_hub sentence-transformers arxiv langchain_community bitsandbytes accelerate
import json
import os
import re

from langchain_community.vectorstores import Chroma
from langchain_core.documents import Document
from langchain_community.embeddings import HuggingFaceEmbeddings
from unstructured_client import UnstructuredClient
from unstructured_client.models import operations, shared
from unstructured_client.models.errors import SDKError
import arxiv
import tqdm
import glob
from unstructured.staging.base import dict_to_elements
from unstructured.chunking.title import chunk_by_title
from typing import List
from langchain_community.vectorstores.utils import filter_complex_metadata

from huggingface_hub.hf_api import HfFolder

# Add your Hugging Face token here


from langchain.prompts import PromptTemplate
from langchain_community.llms import HuggingFacePipeline
from transformers import pipeline
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from langchain.chains import RetrievalQ<PERSON>


def get_arxiv_paper_texts(query: str, max_results: int = 10) -> List[str]:
    # Get list of arxiv papers matching given query using Arxiv API
    arxiv_client = arxiv.Client()

    search = arxiv.Search(
        query=query,
        max_results=max_results,
        sort_by=arxiv.SortCriterion.Relevance,
        sort_order=arxiv.SortOrder.Descending,
    )

    client = UnstructuredClient(api_key_auth=os.getenv("UNSTRUCTURED_API_KEY"))

    paper_texts = []
    # Loop through PDFs, download, pre-process and then delete
    for paper in arxiv_client.results(search):
        paper.download_pdf()
        filename = glob.glob("*.pdf")[0]
        file = open(filename, "rb")

        req = shared.PartitionParameters(
            files=shared.Files(
                content=file.read(),
                file_name=filename,
            ),
            # hi_res strategy is the best choice for complex PDFs (e.g. with tables)
            # and for image-based files
            strategy="hi_res",
        )
        try:
            res = client.general.partition(req)
            if res.elements is not None:
                paper_texts += res.elements

        except SDKError as e:
            print(e)

        # os.remove(filename)
    return paper_texts


def get_documents():
    # elements = get_arxiv_paper_texts("RAG", 10)
    with open("./unstructured_data.json", "r") as gb:
        elements = json.load(gb)
    staged_elements = dict_to_elements(elements)

    chunked_elements = chunk_by_title(
        staged_elements,
        max_characters=512,
        # You can choose to combine consecutive elements that are too small
        # e.g. individual list items
        combine_text_under_n_chars=200,
    )
    documents = []
    for chunked_element in chunked_elements:
        metadata = chunked_element.metadata.to_dict()
        metadata["source"] = metadata["filename"]
        del metadata["languages"]
        documents.append(Document(page_content=chunked_element.text, metadata=metadata))

    return documents


# When partitioning documents, Unstructured enriches the document elements with metadata.
# Here we will use this metadate to extract `paper_id` from `filename` and build a link to the paper on Arxiv


def response_with_links(question, qa_chain):
    sources = []
    response = qa_chain.invoke(question)
    answer = response["result"]
    for source in response["source_documents"]:
        match = re.search(r"(\d+\.\d+)", source.metadata["filename"])
        if match:
            paper_id = match.group(1)

        arxiv_link = f"https://arxiv.org/abs/{paper_id}"
        sources.append(arxiv_link)
    return {"answer": answer, "sources": sources}


def main():
    documents = get_documents()

    # ChromaDB doesn't support complex metadata, e.g. lists, so we drop it here.
    # If you're using a different vector store, you may not need to do this
    documents = filter_complex_metadata(documents)

    embeddings = HuggingFaceEmbeddings(model_name="BAAI/bge-base-en-v1.5")
    vectorstore = Chroma.from_documents(documents, embeddings)
    retriever = vectorstore.as_retriever(search_type="similarity", search_kwargs={"k": 6})

    HfFolder.save_token("*************************************")
    model_name = "meta-llama/Meta-Llama-3-8B-Instruct"

    # The quantized version of the model can run on the free T4 provided in Colab.
    # Without quantization, you will need a beefier machine.

    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.bfloat16,
    )

    model = AutoModelForCausalLM.from_pretrained(model_name, quantization_config=bnb_config)
    tokenizer = AutoTokenizer.from_pretrained(model_name)

    terminators = [tokenizer.eos_token_id, tokenizer.convert_tokens_to_ids("<|eot_id|>")]

    text_generation_pipeline = pipeline(
        model=model,
        tokenizer=tokenizer,
        task="text-generation",
        temperature=0.2,
        do_sample=True,
        repetition_penalty=1.1,
        return_full_text=False,
        max_new_tokens=200,
        eos_token_id=terminators,
    )

    llm = HuggingFacePipeline(pipeline=text_generation_pipeline)

    prompt_template = """
    <|start_header_id|>user<|end_header_id|>
    You are an assistant for answering questions using provided context.
    You are given the extracted parts of a long document and a question. Provide a conversational answer.
    If you don't know the answer, just say "I do not know." Don't make up an answer.
    Question: {question}
    Context: {context}<|eot_id|><|start_header_id|>assistant<|end_header_id|>
    """

    prompt = PromptTemplate(
        input_variables=["context", "question"],
        template=prompt_template,
    )

    qa_chain = RetrievalQA.from_chain_type(
        llm,
        retriever=retriever,
        # Set return_source_documents to True to include the retrieved documents in a response
        return_source_documents=True,
        chain_type_kwargs={"prompt": prompt},
    )

    llm_response = response_with_links("What is a RAG system?", qa_chain)
    print(llm_response["answer"])
    print("Sources: ")
    print(llm_response["sources"])


if __name__ == "__main__":
    prompt_template = """
    <|start_header_id|>user<|end_header_id|>
    You are an assistant for answering questions using provided context.
    You are given the extracted parts of a long document and a question. Provide a conversational answer.
    If you don't know the answer, just say "I do not know." Don't make up an answer.
    Question: {question}
    Context: {context}<|eot_id|><|start_header_id|>assistant<|end_header_id|>
    """

    prompt = PromptTemplate(
        input_variables=["context", "question"],
        template=prompt_template,
    )
    print(prompt)
    main()
