# https://docs.llamaindex.ai/en/stable/examples/retrievers/reciprocal_rerank_fusion/
import os

from llama_index.core import SimpleDirectoryReader

import openai


from llama_index.core import VectorStoreIndex
from llama_index.core.node_parser import SentenceSplitter
from llama_index.embeddings.openai import OpenAIEmbedding

openai.api_key = os.environ["OPENAI_API_KEY"]
openai.base_url = os.environ["OPENAI_BASE_URL"]

documents = SimpleDirectoryReader("/Users/<USER>/workspace/xx/data/paul_graham").load_data()
splitter = SentenceSplitter(chunk_size=256)
index = VectorStoreIndex.from_documents(
    documents,
    transformations=[splitter],
    embed_model=OpenAIEmbedding(model_name="text-embedding-3-small"),
)


QUERY_GEN_PROMPT = (
    "You are a helpful assistant that generates multiple search queries based on a "
    "single input query. Generate {num_queries} search queries, one on each line, "
    "related to the following input query:\n"
    "Query: {query}\n"
    "Queries:\n"
)

from llama_index.retrievers.bm25 import BM25Retriever
from llama_index.core.retrievers import QueryFusionRetriever

vector_retriever = index.as_retriever(similarity_top_k=2)

bm25_retriever = BM25Retriever.from_defaults(docstore=index.docstore, similarity_top_k=2)


retriever = QueryFusionRetriever(
    [vector_retriever, bm25_retriever],
    similarity_top_k=2,
    num_queries=4,  # set this to 1 to disable query generation
    mode="reciprocal_rerank",
    use_async=True,
    verbose=True,
    # query_gen_prompt="...",  # we could override the query generation prompt here
)

# apply nested async to run in a notebook
import nest_asyncio

nest_asyncio.apply()

nodes_with_scores = retriever.retrieve("What happened at Interleafe and Viaweb?")

for node in nodes_with_scores:
    print(f"Score: {node.score:.2f} - {node.text}...\n-----\n")


#
# from llama_index.core.query_engine import RetrieverQueryEngine
#
# query_engine = RetrieverQueryEngine.from_args(retriever)
# response = query_engine.query("What happened at Interleafe and Viaweb?")
# from llama_index.core.response.notebook_utils import display_response
#
# display_response(response)
