from langchain import SelfAskWithSearchChain, VectorDBQA
from langchain.agents import load_tools, initialize_agent
from langchain.chains.summarize import load_summarize_chain
from langchain.document_loaders import UnstructuredFileLoader, DirectoryLoader
from langchain.embeddings import OpenAIEmbeddings, HuggingFaceEmbeddings
from langchain.llms import OpenAI
from langchain.text_splitter import (
    RecursiveCharacterTextSplitter,
    CharacterTextSplitter,
)
from langchain.vectorstores import Chroma

from utils import DATA_PATH


def main():
    llm = OpenAI(model_name="text-davinci-003", max_tokens=1024)
    res = llm("怎么评价人工智能")
    print(res)


def google_search_demo():
    # 加载 OpenAI 模型
    llm = OpenAI(temperature=0, max_tokens=2048)

    # 加载 serpapi 工具
    tools = load_tools(["serpapi"])

    # 如果搜索完想再计算一下可以这么写
    # tools = load_tools(['serpapi', 'llm-math'], llm=llm)

    # 如果搜索完想再让他再用python的print做点简单的计算，可以这样写
    tools = load_tools(["serpapi", "python_repl"])

    # 工具加载后都需要初始化，verbose 参数为 True，会打印全部的执行详情
    agent = initialize_agent(tools, llm, agent="zero-shot-react-description", verbose=True)

    # 运行 agent
    agent.run("What's the date today? What great events have taken place today in history?")


def summarize_long_text():
    # 导入文本
    file_path = DATA_PATH / "files" / "caixin-17.txt"
    loader = UnstructuredFileLoader(file_path.as_posix())
    # 将文本转成 Document 对象
    document = loader.load()
    print(f"documents:{len(document)}")

    # 初始化文本分割器
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=0)

    # 切分文本
    split_documents = text_splitter.split_documents(document)
    print(f"documents:{len(split_documents)}")

    # 加载 llm 模型
    llm = OpenAI(model_name="text-davinci-003", max_tokens=1500)

    # 创建总结链
    chain = load_summarize_chain(llm, chain_type="refine", verbose=True)

    # 执行总结链，（为了快速演示，只总结前5段）
    chain.run(split_documents[:5])


def qa_demo():
    # 加载文件夹中的所有txt类型的文件
    file_path = DATA_PATH / "files"
    persist_directory = DATA_PATH / "persist"

    loader = DirectoryLoader(file_path.as_posix(), glob="**/*.pdf")
    # 将数据转成 document 对象，每个文件会作为一个 document
    documents = loader.load()

    # 初始化加载器
    text_splitter = CharacterTextSplitter(chunk_size=100, chunk_overlap=0)
    # 切割加载的 document
    split_docs = text_splitter.split_documents(documents)

    # 初始化 openai 的 embeddings 对象
    embeddings = OpenAIEmbeddings()
    # embeddings = HuggingFaceEmbeddings()

    # 将 document 通过 openai 的 embeddings 对象计算 embedding 向量信息并临时存入 Chroma 向量数据库，用于后续匹配查询
    # docsearch = Chroma.from_documents(split_docs, embeddings)

    # 持久化数据
    docsearch = Chroma.from_documents(split_docs, embeddings, persist_directory=persist_directory.as_posix())
    docsearch.persist()

    # 加载数据
    # docsearch = Chroma(persist_directory=persist_directory.as_posix(), embedding_function=embeddings)

    # 创建问答对象
    qa = VectorDBQA.from_chain_type(
        llm=OpenAI(),
        chain_type="stuff",
        vectorstore=docsearch,
        return_source_documents=True,
    )
    # 进行问答
    result = qa({"query": "权益分派期间是多少？"})
    print(result)
    result = qa({"query": "最大的小麦出口国是？"})
    print(result)
    result = qa({"query": "全球粮食价格仍会处在较高位置的原因？"})
    print(result)


if __name__ == "__main__":
    # main()
    # google_search_demo()
    # summarize_long_text()
    qa_demo()
