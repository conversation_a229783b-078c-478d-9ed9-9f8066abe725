import asyncio

import httpx
import openai
import requests
import json

# Enter your API key here
api_key = ""


def test_35():
    openai.api_key = api_key

    response = openai.Completion.create(
        # model="text-davinci-003",
        # model="gpt-3.5-turbo",
        prompt="I am a highly intelligent question answering bot. "
        "If you ask me a question that is rooted in truth, I will give you the answer. "
        'If you ask me a question that is nonsense, trickery, or has no clear answer, I will respond with "Unknown".\n\n'
        "Q: What is human life expectancy in the United States?\n"
        "A: Human life expectancy in the United States is 78 years.\n\n"
        "Q: Who was president of the United States in 1955?\n"
        "A: <PERSON> was president of the United States in 1955.\n\n"
        "Q: Which party did he belong to?\n"
        "A: He belonged to the Republican Party.\n\n"
        "Q: What is the square root of banana?\n"
        "A: Unknown\n\n"
        "Q: How does a telescope work?\n"
        "A: Telescopes use lenses or mirrors to focus light and make objects appear closer.\n\n"
        "Q: Where were the 1992 Olympics held?\n"
        "A: The 1992 Olympics were held in Barcelona, Spain.\n\n"
        "Q: How many squigs are in a bonk?\n"
        "A: Unknown\n\n"
        "Q: Where is the Valley of Kings?\n"
        "A: The Valley of Kings is located in Luxor, Egypt.\n\n"
        "Q: How many championships does LeBron James have\n",
        temperature=0,
        max_tokens=100,
        top_p=1,
        frequency_penalty=0.0,
        presence_penalty=0.0,
        stop=["\n"],
    )

    for i in response.choices:
        print(i.text)


def test1():
    # GPT API endpoint
    url = "https://api.openai.com/v1/engines/davinci-codex/completions"

    # Input data for text generation
    input_data = {
        "prompt": "how can i use chatgpt api for my python program",
        "max_tokens": 60,
        "temperature": 0.5,
    }

    # Send a POST request to the GPT API endpoint with input data and API key
    response = requests.post(url, headers={"Authorization": f"Bearer {api_key}"}, json=input_data)

    # Parse the JSON response
    response_json = json.loads(response.text)
    generated_text = response_json["choices"][0]["text"]

    print(generated_text)


def test_351():
    openai.api_key = api_key

    response = openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Who won the world series in 2020?"},
            {
                "role": "assistant",
                "content": "The Los Angeles Dodgers won the World Series in 2020.",
            },
            {"role": "user", "content": "Where was it played?"},
            # {"role": "user", "content": "勒布朗詹姆斯赢了几个总冠军"},
            {"role": "user", "content": "学习python fastapi 有哪些教程推荐"},
        ],
    )

    for i in response.choices:
        print(str(i["message"]["content"]))


async def call_chatgpt_proxy():
    url = "http://100.64.0.32:18888/api/v1/complete"
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
    }

    json_data = {
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant.",
            },
            {"role": "user", "content": "Who won the world series in 2020?"},
            {
                "role": "assistant",
                "content": "The Los Angeles Dodgers won the World Series in 2020.",
            },
            {"role": "user", "content": "Where was it played?"},
            {"role": "user", "content": "勒布朗詹姆斯赢了几个总冠军"},
        ],
        "user_id": 0,
        "app": "string",
        "ratio": 1,
    }

    response = requests.post(url, headers=headers, json=json_data)
    print(response.json())

    async with httpx.AsyncClient(verify=False) as client:
        try:
            resp = await client.post(url, json=json_data, headers=headers)
            print(resp.json())
        except Exception as e:
            print(e)


def openai_function_calling_demo():
    openai.api_key = api_key

    response = openai.ChatCompletion.create(
        model="gpt-3.5-turbo-0613",
        messages=[
            {"role": "user", "content": "What is the weather like in Boston?"},
        ],
        functions=[
            {
                "name": "get_current_weather",
                "description": "Get the current weather in a given location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city and state, e.g. San Francisco, CA",
                        },
                        "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]},
                    },
                    "required": ["location"],
                },
            }
        ],
    )

    for i in response.choices:
        print(str(i["message"]["content"]))


if __name__ == "__main__":
    # test_351()
    openai_function_calling_demo()
    # asyncio.run(call_chatgpt_proxy())
