# import base64
# import json
# import logging
# from http import HTTPStatus
# from typing import List, Optional
#
# import requests
# from fastapi import Depends, Form, HTTPException, UploadFile
# from llama_index.core.output_parsers import PydanticOutputParser
# from pydantic import BaseModel, Field, ValidationError, create_model
#
# from remarkable.routers.external import router
# from remarkable.services.chatgpt import OpenAIClient, safe_load
#
# logger = logging.getLogger(__name__)
#
#
# DATA_MESSAGES_MAP = {
#     "KPI A1.4 - non-hazardous waste": {
#         "06806": [  # 2021年  http://***********:55647/#/project/remark/196725?projectId=19&treeId=42808&fileId=63681&schemaId=2&schemaKey=KPI%20A1.4
#             {
#                 "index": 597,
#                 "text": "|Indicator|Unit|2021|2020|2019|\n|-|-|-|-|-|\n|Indicator|Unit|Emissions|2020|2019|\n|Total GHG emissions (Scope 1 and Scope 2)|CO2 equivalent (t)|11,248|8,988|9,513|\n|Greenhouse gas emissions per unit area|CO2 equivalent (t/m2)|0.18|0.14|0.15|\n|Direct GHG emissions (Scope 1)|CO2 equivalent (t)|431|287|331|\n|Indirect GHG emissions (Scope 2)|CO2 equivalent (t)|10,817|8,701|9,182|\n|||Wastes|||\n|Household waste|t|81|699|328|\n|Food waste|t|150.9|413|258|\n|Waste ofice paper|t|21.05|38|30|\n|Toner cartridge|/|1,810|1,369|1,063|\n|||Use of Resources|||\n|Natural gas consumption|m3|169,057|104,193|114,228|\n|Natural gas consumption per unit area|m3/m2|2.69|1.66|1.82|\n|Mileage of company cars|km|170,918|188,748|204,236|\n|Gasoline consumption|L|27,114|30,453|39,521|\n|Gasoline consumption per unit area|L/m2|0.43|0.48|0.63|\n|Diesel consumption|L|912|685|/|\n|Diesel consumption per unit area|L/m2|0.02|0.01|/|\n|Power consumption|MWh|9,832.97|11,456|11,282|\n",
#                 "class": "TABLE",
#                 "score": 0.14361579100630106,
#                 "syllabus": "Environmental performance",
#                 "table_title": "Environmental performance",
#             }
#         ],
#         "00423": [  # 2022年  https://jura.paodingai.com/#/hkex/esg-report-checking/report-review/226099?fileId=70994&schemaId=2&rule=KPI%20A1.4%20-%20non-hazardous%20waste
#             {
#                 "index": 32,
#                 "text": "|Key Performance Indicators|Description|FY2020/2021 Data|FY2021/2022 Data|\n|-|-|-|-|\n|KPI A1.1|Types of air emissions:The Group’s air emissions mainly include nitrogen oxides (“NOx”), sulphur oxides (“SOx”) and respiratory suspended particles (“PM”) generated by the use of the motor vehicles owned by the Group.|NO: 417.26 kg xSO: 0.70 kg xPM: 38.18 kg|NO: 410.29 kg xSO: 0.70 kg xPM: 37.54 kg|\n|KPI A1.2|Greenhouse gas (GHG) emissions:|Scope 1: 729.95tCO2e-|Scope 1: 733.79tCO2e-|\n|KPI A1.2|Scope 1 – The Group’s direct GHG emissions are mainly by stationary combustionof liquefied petroleum gas (LPG), as well as combustion of fuels in motor vehicles owned by the Group.|Scope 1: 729.95tCO2e-|Scope 1: 733.79tCO2e-|\n|KPI A1.2|Scope 2 – The Group’s GHG emissions are primarily indirect emissions from electricity consumptions.|Scope 2: 5,094.58 tCO2e-|Scope 2: 5,205.35 tCO2e-|\n|KPI A1.2|Scope 3 – GHG emissions from business air travel are not material and therefore not reported.|Intensity: 0.01 tCO2e-/HK$ ‘000|Intensity: 0.01 tCO2e-/HK$ ‘000|\n|KPI A1.2|GHG emissions from disposal of paper waste are not material and therefore not reported.|N/A|N/A|\n|KPI A1.3|Hazardous waste produced:|1,960 Litres|400 Litres|\n|KPI A1.3|The Group’s printing operations generate hazardous waste such as spent ink, and developed plates.|1,000 kg|200 kg|\n|KPI A1.4|Non-hazardous waste produced:The Group’s non-hazardous wastes produced are mainly general office wastes and are not material.|NA|NA|\n",
#                 "class": "TABLE",
#                 "score": 0.5646740170234357,
#                 "syllabus": "Aspect A1: Emissions",
#                 "table_title": "The printing operations of the Group release chemical wastes. The printing plants are registered as chemical waste producers with the Environmental Protection Department. All the chemical wastes are collected by licensed chemical waste collectors in compliance with the relevant regulations in Hong Kong. The printing plant had erected a Regenerative Thermal Oxidizer to reduce over 90% of the volatile organic compounds emissions generated during the printing process. Our printing plant has also adopted a non-alcohol printing method to reduce the use of chemicals.|Aspect A1: Emissions",
#             },
#             {
#                 "index": 34,
#                 "text": "|Key Performance Indicators|FY2020/2021    FY2021/2022 Description                    Data          Data|\n|-|-|\n|KPI A1.5|Measures to mitigate emissions and results: The Regenerative Thermal Oxidizer erected in our printing plant reduces over 90% of the volatile organic compounds emissions generated during the printing process. The Group also adopted energy saving measures as described in Aspect A2 which in turn reduce the GHG emissions.|\n|KPI A1.6|Handling of hazardous and non-hazardous waste: All the chemical wastes produced by the Group’s printing plants are collected by licensed chemical waste collectors in compliance with the relevant regulations in Hong Kong. Waste paper is collected by a recycling company.|\n",
#                 "class": "TABLE",
#                 "score": 0.11297919326238974,
#                 "syllabus": "Aspect A1: Emissions",
#                 "table_title": "The printing operations of the Group release chemical wastes. The printing plants are registered as chemical waste producers with the Environmental Protection Department. All the chemical wastes are collected by licensed chemical waste collectors in compliance with the relevant regulations in Hong Kong. The printing plant had erected a Regenerative Thermal Oxidizer to reduce over 90% of the volatile organic compounds emissions generated during the printing process. Our printing plant has also adopted a non-alcohol printing method to reduce the use of chemicals.|Aspect A1: Emissions",
#             },
#         ],
#         "08511": [  # 2022 https://jura.paodingai.com/#/hkex/esg-report-checking/report-review/226430?fileId=71325&schemaId=2&rule=KPI%20A1.4%20-%20non-hazardous%20waste
#             {
#                 "index": 114,
#                 "text": "Non-hazardous wastes produced during the Reporting Period were mainly general office wastes and domestic refuse, which included used paper, used stationery and etc. All these wastes were collected and disposed of properly. There is no applicable data of non-hazardous wastes produced from our operations, as waste is collected and handled by designated service provider hired by the property management company of the commercial building where our office is located.",
#                 "class": "PARAGRAPH",
#                 "score": 0.3956131776620163,
#                 "syllabus": "Non-hazardous waste",
#             }
#         ],
#     },
#     "KPI A2.4 part 1 - water sourcing": {
#         "01760": [
#             {
#                 "index": 211,
#                 "text": "The water used by the Group comes from the municipal water supply, and there is no problem in sourcing water. Although the management office of the building manages the reporting boundaries, we are nevertheless working to reduce water use to the extent feasible. Our water conservation measures include:",
#                 "class": "PARAGRAPH",
#                 "score": 0.7722010020773629,
#                 "syllabus": "6.3 Water Conservation",
#             }
#         ],
#         "01631": [
#             {
#                 "index": 109,
#                 "text": "As the Group’s principal businesses are providing financial printing services, it does not consume a significant amount of water during operation. The Group’s water consumption was mainly attributable to water consumed in office. Despite water consumption is considered insignificant due to business nature, the Group is dedicated to promoting behavioural changes in water usage at offices and encouraging water conservation. Environmental signages on water-saving messages are posted in prominent places to remind employees to conserve water. Due to operating locations, the Group did not encounter any significant issue in sourcing water that is fit for purpose. With the above measures implemented, employees’ awareness on water conservation has been enhanced.",
#                 "class": "PARAGRAPH",
#                 "score": 0.8748017806592936,
#                 "syllabus": "Water Consumption",
#             },
#             {
#                 "index": 113,
#                 "text": "|Indicator|Unit|2021|2020|\n|-|-|-|-|\n|Water consumption|m3|109.00|265.47|\n|Intensity|m3/square feet|0.005|0.01|\n",
#                 "class": "TABLE",
#                 "score": "",
#                 "syllabus": "Water Consumption",
#                 "table_title": "During the Year, the Group’s water consumption performance is as follows:|The Group’s water consumption intensity (m3/square feet) has a decrease of approximately 50.0% from approximately 0.01 in 2020 to approximately 0.005 in 2021 due to the increased employees’ awareness on water conservation. In addition, the reallocated office’s water usage is included in the tenancy fee, and therefore water consumption data of the reallocated office was not available.",
#             },
#         ],
#     },
#     "KPI B4.1 - review measures to avoid child & forced labour": {
#         "03692": [  # http://***********:55647/#/project/remark/196734?projectId=19&treeId=42818&fileId=63691&schemaId=2&schemaKey=B4.1
#             {
#                 "index": 560,
#                 "text": "Talents are the core capital for the sustainable development of an enterprise. Adhering to equal employment and equal pay for equal work, we are committed to creating a fair and just employment environment for employees, prohibit employment discrimination, respect and fairly treat employees of different genders, ages, educational backgrounds, ethnicities, religious beliefs and cultural backgrounds. In strict compliance with the Labor Law of the People’s Republic of China (中華人民共和國勞動法), the Labor Contract Law of the People’s Republic of China (中華人民共和國勞動合同法) and other laws and regulations, we have formulated the Employee Diversity Policy (員工多元化政策). Based on clear and definite talent selection criteria, adopting professionally adapted appraisal methods, we carry out talent recruitment in an orderly manner, widely introduce diverse outstanding talents, enabling the business to develop steadily. We insist on standardized employment and prohibit the employment of child labor or forced labor. In the process of employee recruitment and induction, we strictly abide by laws and regulations such as the Law of the People’s Republic of China on the Protection of Minors (中華人民共和國未成年人保護法) and the Provisions on Prohibition of the Use of Child Labor (禁止使用童工規定). We strictly review the information of the applicants at each process of employee recruitment to ensure that all employees are of legal working age. We strictly comply with the relevant requirements in our Employee Handbook, which prohibits any form of discrimination and harassment. Once violations are identified, we take the zero-tolerance approach and impose behavioral corrections and disciplinary actions on the personnel involved. Meanwhile, we advocate work-life balance of employees, and encourage employees to efficiently and responsibly complete their work during working hours. Overtime is not encouraged, forced labor is not allowed, so as to guarantee a reasonable rest time for employees. Hansoh Pharma regularly conducts inspections on child labor and forced labor. Once any violations of laws and regulations are identified, they will be dealt with timely and severely. During the Reporting Period, there was no employment of child labor or forced labor nor any event of employee discrimination or harassment found in Hansoh Pharma.",
#                 "class": "PARAGRAPH",
#                 "score": 0.8554295213414668,
#                 "syllabus": "8.1 EQUAL EMPLOYMENT",
#             },
#             {
#                 "index": 653,
#                 "text": "",
#                 "class": "IMAGE",
#                 "score": "",
#                 "syllabus": "“Face-to-Face” Communication Activities For Employees",
#             },
#             {
#                 "index": 562,
#                 "text": "employees aged < 30: 5,750 employees aged > 50: 215 employees aged 30-50: 6,185 mainland China: 12,088 overseas: 59 Hong Kong, Macao and Taiwan, China: 3",
#                 "class": "INFOGRAPHIC",
#                 "score": "",
#                 "syllabus": "8.1 EQUAL EMPLOYMENT",
#             },
#             {
#                 "index": 676,
#                 "text": "Series of Activities During the International Women’s Day Festival and their families best embrace the new family member. During the International Women’s Day Festival, we launched “Red-flag Bearer for the",
#                 "class": "INFOGRAPHIC",
#                 "score": "",
#                 "syllabus": "8.3.4 Diversity and Inclusion",
#             },
#         ],
#         "02269": [  # http://***********:55647/#/project/remark/196794?projectId=19&treeId=42877&fileId=63750&schemaId=2&schemaKey=B4.1
#             {
#                 "index": 497,
#                 "text": "We strictly abide by our Recruitment Management Policy and local laws in all the locations in which we operate, and continually work to refine recruitment procedures. To match candidates with suitable jobs, we practice fair competition and needs-based talent searches. We treat every applicant equally, and do not discriminate on the basis of ethnicity, gender, race, region, religion, form of employment, or any other factors. Child labor and forced labor are forbidden; in 2021, no such labor was used.",
#                 "class": "PARAGRAPH",
#                 "score": 0.24909322269967235,
#                 "syllabus": "Acquiring Talent",
#             },
#             {
#                 "index": 586,
#                 "text": "In 2021, LTIR7 was 0.042 of the company, and all the work injuries have been properly resolved.",
#                 "class": "INFOGRAPHIC",
#                 "score": "",
#                 "syllabus": "Minimizing Work Accidents",
#             },
#             {
#                 "index": 508,
#                 "text": "Our corporate culture and talent management are widely recognized by the community. In 2021,",
#                 "class": "INFOGRAPHIC",
#                 "score": "",
#                 "syllabus": "Female Employees",
#             },
#         ],
#         "01711": [
#             {
#                 "index": 134,
#                 "text": "The Group fully complies with relevant laws and regulations in related regions concerning prevention of forced or child labour. In the recruitment process, the Group implements appropriate procedures to ensure that employment adheres to minimum age provisions of applicable laws. The Group also prohibits any form of forced labour. The ages and identities of its employees are verifi ed, and employment contracts are entered into with all employees.",
#                 "class": "PARAGRAPH",
#                 "score": 0.6785780947345326,
#                 "syllabus": "3.2 Labour Standard ",
#             },
#             {
#                 "index": 133,
#                 "text": "To ensure the staff clearly understand their rights and obligations, the employee handbook and other policies and guidelines are in place covering the areas of compensation and dismissal, recruitment, working hours, rest periods, equal opportunity, anti-discrimination and other fringe benefi ts, etc. The Group has been reviewing its related policies from time to time to ensure the Group complies with the latest statutory requirements. Also, a set of grievance procedures is also in place, to provide staff with a channel to confi dentially escalate complaints and concerns to the Human Resources Department.",
#                 "class": "PARAGRAPH",
#                 "score": 0.11586202733133379,
#                 "syllabus": "3.2 Labour Standard ",
#             },
#             {"index": 235, "text": "21", "class": "IMAGE", "score": "", "syllabus": "4.1 Supply Chain Management "},
#             {
#                 "index": 119,
#                 "text": "按年齡≤25 1% 26‐35 22% 36‐45 25% 46‐55 33%≥56 19% FY2021/22年度3% 23% 29% 28% 17% FY2020/21年度By Gender按性別",
#                 "class": "SHAPE",
#                 "score": "",
#                 "syllabus": "3.1 Workforce Distribution and Diversity ",
#             },
#         ],
#     },
#     "KPI B4.2 - steps to avoid child & forced labour": {
#         "03313": [  # http://***********:55647/#/project/remark/197420?treeId=43500&fileId=64376&schemaId=2&projectId=43974&schemaKey=KPI%20B4.2%20-%20steps%20to%20avoid%20child%20&%20forced%20labour
#             {
#                 "index": 222,
#                 "text": "Upon discovery of any child labour, the HR department would immediately remove the child from the workplace and arrange for the child to have a special labour health check to make sure the health condition is not affected. Further, the HR department would contact the family and send the child home. All expenses related to medical and transportation would be covered by the Group.",
#                 "class": "PARAGRAPH",
#                 "score": 0.6894395550695172,
#                 "syllabus": "Child Labour Remediation Measures",
#             },
#             {
#                 "index": 221,
#                 "text": "Child Labour Remediation Measures",
#                 "class": "PARAGRAPH",
#                 "score": 0.3516269223183817,
#                 "syllabus": "Child Labour Remediation Measures",
#             },
#             {
#                 "index": 220,
#                 "text": "The Group is committed to upholding the labour rights of staff and has established a compliant mechanism for staff to report any labour violations. It is always the Group’s policy to prohibit the employment of staff members under the legal working age of 18. At the time of the interview, the human resources department requests the job applicants to provide valid identity documents for the verification. Furthermore, the employment contracts state clearly the employment terms and conditions in accordance with essential legal requirements.",
#                 "class": "PARAGRAPH",
#                 "score": 0.1688245048270632,
#                 "syllabus": "4.4. Labor Standards",
#             },
#             {
#                 "index": 224,
#                 "text": "It is the Group’s policy to disqualify the person from employment if he or she is found to be hired against the requirements of the Labour Contract Law. For the year ended 31 December 2021, there was no labour dispute in the Group.",
#                 "class": "PARAGRAPH",
#                 "score": 0.14826716582612798,
#                 "syllabus": "Child Labour Remediation Measures",
#             },
#         ],
#         "01631": [  # http://***********:55647/#/project/remark/197444?treeId=43524&fileId=64400&schemaId=2&projectId=43974&schemaKey=KPI%20B4.2%20-%20steps%20to%20avoid%20child%20&%20forced%20labour
#             {
#                 "index": 196,
#                 "text": "The Group will conduct investigations, punishment or dismissal of relevant employees immediately when any non-compliance is being discovered. If necessary, the Group will further improve the labour mechanism against illegal behaviours.",
#                 "class": "PARAGRAPH",
#                 "score": 0.5870605741717001,
#                 "syllabus": "Prevention of Child Labour and Forced Labour",
#             },
#             {
#                 "index": 195,
#                 "text": "The Group strictly prohibits the use of child labour by stringently reviewing the actual age of the interviewee during the recruitment process, including the examination of identity documents and detailed records. The Group only carries out the requirements of standard labour contract and does not utilise any other means to unfairly restrict the employment relationship between employee and the Group.",
#                 "class": "PARAGRAPH",
#                 "score": 0.17860088934601534,
#                 "syllabus": "Prevention of Child Labour and Forced Labour",
#             },
#         ],
#     },
#     "KPI B5.1 - number of suppliers": {
#         "09608": [  # http://***********:55647/#/project/remark/196708?projectId=19&treeId=42790&fileId=63663&schemaId=2&schemaKey=KPI%20B5.1%20
#             {
#                 "index": 462,
#                 "text": "|Environmental, Social and Governance Reporting Guide|Environmental, Social and Governance Reporting Guide|Content in the Report|\n|-|-|-|\n|B2.1|Number and rate of work-related fatalities occurred in each of the past three years, including the reporting year.|6.3 Protection of of rights and interests|\n|B3|General DisclosurePolicies on improving employees’ knowledge and skills for discharging duties at work. Description of training activities.|6.2 Training across all dimensions|\n|B4|General DisclosureInformation on:(a) the policies; and(b) compliance with relevant laws and regulations that have a significant impact on the issuer relating to preventing child and forced labour.|6.1 Employment compliance|\n|B5|B5|B5|\n|B5.1|Number of suppliers by geographical region.|4.2 Supplier management|\n",
#                 "class": "index_table",
#             },
#             {
#                 "index": 112,
#                 "text": "Number of Suppliers by Geographic Distribution",
#                 "class": "PARAGRAPH",
#                 "score": 0.6139052173351751,
#                 "syllabus": "4.2 SUPPLIER MANAGEMENT",
#             },
#             {
#                 "index": 113,
#                 "text": "78 21 20 310 50 11 4 10 385 Anhui Jiangsu Shanghai Hangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang",
#                 "class": "SHAPE",
#                 "score": "",
#                 "syllabus": "4.2 SUPPLIER MANAGEMENT",
#             },
#             {
#                 "index": 481,
#                 "text": "|ESG indexes|ESG indexes|Data in 2021|\n|-|-|-|\n||Per capita training duration for female employee (hours)|95.50|\n||Per capita training duration for senior management employee (hours)|24.00|\n||Per capita training duration for primary and middle management employee (hours)|88.50|\n||Per capita training duration for grassroots employee (hours)|95.50|\n||Report of death on duty|0|\n|Employee health and safety|Number of working days of person injured on duty (days)|60|\n|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|504|\n|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|0|\n|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|78|\n|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|21|\n|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|Number of suppliers (units) Number of withdrawn suppliers (units) Anhui Jiangsu Shanghai|20|\n|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|310|\n|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|50|\n|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|11|\n|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|4|\n|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|Conditions of suppliersHangzhou Zhejiang Zhoushan Zhejiang Ningbo Zhejiang Huzhou Zhejiang Jinhua Zhejiang|10|\n",
#                 "class": "TABLE",
#                 "score": 0.1783351396519768,
#                 "syllabus": "8.3 ESG KEY PERFORMANCE TABLE",
#                 "table_title": "8.3 ESG KEY PERFORMANCE TABLE|8. APPENDIX",
#             },
#             {
#                 "index": 478,
#                 "text": "|ESG indexes|ESG indexes|Data in 2021|\n|-|-|-|\n|Employee promotion|Promotion of employee (person)|55|\n|Employee promotion|Promotion of general employee|33|\n|Employee promotion|Promotion of primary management personnel (persons)|16|\n|Employee promotion|Promotion of middle management personnel (persons)|4|\n|Employee promotion|Promotion of senior management personnel (persons)|2|\n|Employee training|Total number of male employees training (persons)|293|\n|Employee training|Total number of female employees training (persons)|383|\n|Employee training|Percentage of male employees training (%)|100.00%|\n|Employee training|Percentage of female employees training (%)|96.23%|\n|Employee training|Total number of senior management employee training (persons)|17|\n|Employee training|Total number of primary and middle management employee training (persons)|146|\n|Employee training|Total number of grassroots employee training (persons)|509|\n|Employee training|Percentage of senior management employee training (%)|52.67%|\n|Employee training|Percentage of middle management employee training (%)|100.00%|\n|Employee training|Percentage of grassroots employee training (%)|87.46%|\n|Employee training|Per capita training duration for male employee (hours)|93.50|\n",
#                 "class": "TABLE",
#                 "score": 0.11214407263748738,
#                 "syllabus": "8.3 ESG KEY PERFORMANCE TABLE",
#                 "table_title": "8.3 ESG KEY PERFORMANCE TABLE|8. APPENDIX",
#             },
#             {
#                 "index": 282,
#                 "text": "42% 21% 6% 8% 6% 2% 15% Zhejiang Anhui Jiangsu Henan Guangxi Jiangxi Other regions",
#                 "class": "SHAPE",
#                 "score": "",
#                 "syllabus": "GEOGRAPHICAL DISTRIBUTION OF EMPLOYEES (%)",
#             },
#         ],
#         "03692": [  # http://***********:55647/#/project/remark/196734?projectId=19&treeId=42818&fileId=63691&schemaId=2&schemaKey=KPI%20B5.1%20
#             {
#                 "index": 921,
#                 "text": "|Subject Areas of Environmental, Social and Governance and General Disclosures and KPIs|Subject Areas of Environmental, Social and Governance and General Disclosures and KPIs|Subject Areas of Environmental, Social and Governance and General Disclosures and KPIs|Chapter|\n|-|-|-|-|\n|B5: Supply Chain Management|KPI B5.1|Number of suppliers by geographical region.|Green Supply and Win-win Cooperation–  Supplier Admission|\n",
#                 "class": "index_table",
#             },
#             {
#                 "index": 886,
#                 "text": "|Social Performance Indicators|Social Performance Indicators|Unit|Data for Performance Indicators|\n|-|-|-|-|\n|Q12 evaluation4 on employee satisfaction rate|Q12 evaluation4 on employee satisfaction rate|%|83.83|\n|Q12 evaluation on employee engagement rate|Q12 evaluation on employee engagement rate|%|83.8|\n|Percentage of employees receiving regular performance and career development appraisals|Percentage of employees receiving regular performance and career development appraisals|%|100|\n|Percentage of vacancies filled by internal candidates|Percentage of vacancies filled by internal candidates|%|32|\n|Diversity|Diversity|||\n|Proportion of females in each position|Board|%|50|\n|Proportion of females in each position|Executive management|%|41|\n|Proportion of females in each position|Senior management|%|29|\n|Proportion of females in each position|Junior management|%|35|\n|Proportion of females in each position|All management posts|%|30|\n|Proportion of female management personnel in revenue generating department5|Proportion of female management personnel in revenue generating department5|%|26|\n|Proportion of females in STEM6 related positions|Proportion of females in STEM6 related positions|%|49.0|\n|Incidents related to the use of child labor or forced labor|Incidents related to the use of child labor or forced labor|Number of cases|0|\n|Suppliers||||\n|Number of suppliers||Number|5,073|\n||Mainland China|Number|4,974|\n|By region|Hong Kong, Macao and Taiwan|Number|5|\n|By region|Overseas|Number|94|\n|Code of Business Conduct coverage|Code of Business Conduct coverage|%|100|\n|Localized procurement proportion7|Localized procurement proportion7|%|40.4|\n|Rate of lost-time injury for contractors (per million hours of works)|Rate of lost-time injury for contractors (per million hours of works)|Number of injuries/million hours of works|0|\n",
#                 "class": "TABLE",
#                 "score": 0.2862769812472753,
#                 "syllabus": "Appendix II Summary of Indices and Indicators",
#                 "table_title": "3 The formula for calculating the percentage of employees trained in different categories: the number of|Appendix II Summary of Indices and Indicators",
#             },
#             {
#                 "index": 505,
#                 "text": "As of the end of the Reporting Period, the Group had a wide variety of 5,073 suppliers, including 967 key material suppliers. 100% of suppliers underwent the qualification evaluation and audit. The numbers of suppliers by region were: 4,974 in Mainland China, 5 in Hong Kong, Macau and Taiwan, 94 in overseas regions.",
#                 "class": "PARAGRAPH",
#                 "score": 0.2800725192006578,
#                 "syllabus": "Supplier qualification evaluation system",
#             },
#             {
#                 "index": 893,
#                 "text": "|Social Performance Indicators|Unit|Data for Performance Indicators|\n|-|-|-|\n|Customer Service|||\n|Percentage of products recalled for safety and health reasons|%|0|\n|Number of compliant relating to the quality of the products and services|Number|1|\n|Number of compliant relating to the products and services for other reasons|Number|12|\n|Complaints handling rate|%|100|\n|Customer satisfaction rate|%|92|\n|Intellectual Property|||\n|Number of patents granted (during the Reporting Period)|Number|88|\n|Number of registered trademarks obtained (during the Reporting Period)|Number|92|\n|Contributions to the Employees and the Society|||\n|Expenditure in supporting employees in difficulties|RMB 1 million|1.3|\n|Expenditure in charity donation and other relevant fields|RMB 1 million|64|\n|Hours of voluntary work|Number of participants|860|\n|Hours of voluntary work|Hours|4,560|\n|Codes of Business Conduct|||\n|Number of corruption litigation|Number|0|\n",
#                 "class": "TABLE",
#                 "score": 0.12807703491987163,
#                 "syllabus": "Appendix II Summary of Indices and Indicators",
#                 "table_title": "3 The formula for calculating the percentage of employees trained in different categories: the number of|Appendix II Summary of Indices and Indicators",
#             },
#         ],
#         "01542": [  # http://***********:55647/#/project/remark/196787?projectId=19&treeId=42869&fileId=63742&schemaId=2&schemaKey=KPI%20B5.1%20
#             {
#                 "index": 398,
#                 "text": "|Subject Areas, Aspects, General Disclosures and KPIs|Subject Areas, Aspects, General Disclosures and KPIs| Section/Statement| Page|\n|-|-|-|-|\n|General DisclosurePolicies on improving employees’ knowledge and skills for discharging duties at work. Description of training activities.|General DisclosurePolicies on improving employees’ knowledge and skills for discharging duties at work. Description of training activities.|General DisclosurePolicies on improving employees’ knowledge and skills for discharging duties at work. Description of training activities.|General DisclosurePolicies on improving employees’ knowledge and skills for discharging duties at work. Description of training activities.|\n|KPI B3.1|KPI B3.1|Caring for Employees– Development and Training|28-29|\n|General DisclosureInformation on:(a)    the policies; and(b)   compliance with relevant laws and regulations that have a significant impact on the issuer relating to preventing child and forced labour.|General DisclosureInformation on:(a)    the policies; and(b)   compliance with relevant laws and regulations that have a significant impact on the issuer relating to preventing child and forced labour.|Caring for Employees– Prevention of Child Labour and Forced Labour|29|\n|KPI B4.1| Description of measures to review employment practices to avoid child and forced labour.|Caring for Employees– Prevention of Child Labour and Forced Labour|29|\n|General DisclosurePolicies on managing environmental and social risks of the supply chain.|General DisclosurePolicies on managing environmental and social risks of the supply chain.|General DisclosurePolicies on managing environmental and social risks of the supply chain.|General DisclosurePolicies on managing environmental and social risks of the supply chain.|\n|KPI B5.1|KPI B5.1| Stable Water Supply– Supply Chain Management|20-21|\n|KPI B5.1| Number of suppliers by geographical region.| Stable Water Supply– Supply Chain Management|20-21|\n",
#                 "class": "index_table",
#             },
#             {
#                 "index": 170,
#                 "text": "As of 31 December 2021, the number of our major suppliers was 95 and all of them were from China. All suppliers need to pass our established evaluation process prior to the initiation of formal purchases.",
#                 "class": "PARAGRAPH",
#                 "score": 0.25267341903263435,
#                 "syllabus": "SUPPLY CHAIN MANAGEMENT",
#             },
#         ],
#     },
#     "KPI B6.1 - products recall": {
#         "00560": [  # http://***********:55647/#/project/remark/196680?projectId=19&treeId=42763&fileId=63636&schemaId=2&schemaKey=KPI%20B6.1%20
#             {
#                 "index": 626,
#                 "text": "|General Disclosures and Key Performance Indicators (KPIs)|Descriptions|Relevant Sections and Remarks|\n|-|-|-|\n|General Disclosure|General Disclosure|General Disclosure|\n|General Disclosure|Policies on managing environmental and social risks of the supply chain.|6.4|\n|General Disclosure|(a)   Policies; and(b) compliance with relevant laws and regulations that have a significant impact on the issuerrelating to health and safety, advertising, labelling and privacy matters relating to products and services provided and methods of redress.|6.1, 6.2, Appendix 1* Due to the Group’s business nature, products, labelling relating to the services provided, and recall procedures are not applicable to the Group.|\n|KPI B6.1|Percentage of total products sold or shipped subject to recalls for safety and health reasons.|* The Group does not produce any products.|\n",
#                 "class": "index_table",
#             },
#             {
#                 "index": 226,
#                 "text": "For our customers Regarding the passenger transportation business, in order to address the need for pandemic prevention and control, the Group fully tied in with epidemic prevention policies, operations of the cross-border waterway passenger routes of urban areas.",
#                 "class": "INFOGRAPHIC",
#                 "score": "",
#                 "syllabus": "21 Chu Kong Shipping Enterprises (Group) Company Limited • Environmental, Social and Governance Report 2021",
#             },
#         ],
#         "06998": [  # http://***********:55647/#/project/remark/196646?projectId=19&treeId=42729&fileId=63602&schemaId=2&schemaKey=KPI%20B6.1%20
#             {
#                 "index": 618,
#                 "text": "|General Disclosure and KPIs|Indicator Description|Chapter References|\n|-|-|-|\n|General Disclosure|Information on: (a) the Policy; and (b) compliance with relevant laws and regulations that have a significant impact on the issuer relating to health and safety, advertising, labelling and privacy matters relating to products and services provided and methods of redress.|Information on: (a) the Policy; and (b) compliance with relevant laws and regulations that have a significant impact on the issuer relating to health and safety, advertising, labelling and privacy matters relating to products and services provided and methods of redress.|\n|KPI|B6.1        Percentage of total products sold or shipped subject to recalls for safety and health reasons.|During the Reporting Period, the Group did not sell any products or services, thus, no products were sold or shipped that had to be recalled for safety and health reasons.|\n",
#                 "class": "index_table",
#             },
#             {
#                 "index": 355,
#                 "text": "As a biopharmaceutical company that is entering the commercialisation stage, the Group plans to summary the complaints that will be handled annually, which would help us to propose effective preventive measures to avoid the same quality issues. During the Reporting Period, no products were sold or shipped that had to be recalled for safety and health reasons, nor were there any customer complaints about products and services.",
#                 "class": "PARAGRAPH",
#                 "score": 0.24111966142656852,
#                 "syllabus": "Product Complaint and Recall",
#             },
#             {
#                 "index": 616,
#                 "text": "|General Disclosure and KPIs|General Disclosure and KPIs|General Disclosure and KPIs|General Disclosure and KPIs|\n|-|-|-|-|\n|KPI|B2.1|B2.1|Caring for People and Contributing to Society- Health and Safety|\n|KPI|B2.2|Lost days due to work injury.|Caring for People and Contributing to Society- Health and Safety|\n|KPI|B2.3|Description of occupational health and safety measures adopted, how they are implemented and monitored.|Caring for People and Contributing to Society- Health and Safety|\n|Aspect B3: Development and Training|Aspect B3: Development and Training|Aspect B3: Development and Training|Aspect B3: Development and Training|\n|General Disclosure|Policies on improving employees’ knowledge and skills for discharging duties at work. Description of training activities.|Policies on improving employees’ knowledge and skills for discharging duties at work. Description of training activities.|Preceding Breakthroughs with Excellent Leadership- Driven by Innovative Talents|\n|KPI|B3.1|The percentage of employees trained by gender and employee category (e.g. senior management, middle management).|Preceding Breakthroughs with Excellent Leadership- Driven by Innovative Talents|\n|KPI|B3.2|The average training hours completed per employee by gender and employee category.|Preceding Breakthroughs with Excellent Leadership- Driven by Innovative Talents|\n|Aspect B4: Labour Standards|Aspect B4: Labour Standards|Aspect B4: Labour Standards|Aspect B4: Labour Standards|\n|General Disclosure|Information on: (a) the Policy; and (b) compliance with relevant laws and regulations that have a significant impact on the issuer relating to preventing child and forced labour.|Information on: (a) the Policy; and (b) compliance with relevant laws and regulations that have a significant impact on the issuer relating to preventing child and forced labour.|Caring for People and Contributing to Society- Responsible Employment|\n|KPI|B4.1|Description of measures to review employment practices to avoid child and forced labour.|Caring for People and Contributing to Society- Responsible Employment|\n|KPI|B4.2|Description of steps taken to eliminate such practices when discovered.|Caring for People and Contributing to Society- Responsible Employment|\n|KPI|Aspect B5: Supply Chain Management|Aspect B5: Supply Chain Management|Aspect B5: Supply Chain Management|\n|General Disclosure|Policies on managing environmental and social risks of the supply chain.|Policies on managing environmental and social risks of the supply chain.|Synchronising Quality and Safety Management- Responsible Supply Chain Management|\n",
#                 "class": "TABLE",
#                 "score": 0.10366877618480173,
#                 "syllabus": "A. Environmental",
#                 "table_title": "A. Environmental|aa aaaa aaENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT 2021 Appendix48 STOCK CODE",
#             },
#         ],
#         "06088": [  # http://***********:55647/#/project/remark/196695?projectId=19&treeId=42778&fileId=63651&schemaId=2&schemaKey=KPI%20B6.1%20
#             {
#                 "index": 1445,
#                 "text": "|Aspect|Disclosures|Reporting Chapter|\n|-|-|-|\n|B6.1|Percentage of total products sold or shipped subject to recalls for safety and health reasons|Quality Oriented Performance and Data|\n",
#                 "class": "index_table",
#             },
#             {
#                 "index": 1396,
#                 "text": "||||Unit|2021|2020|\n|-|-|-|-|-|-|\n|Customer Complaints|Product and services complaints||Piece|406,35425|1,161,049|\n|Customer Complaints|Safety and health-related recalls||%|0|0.0254%|\n|Intellectual Property|Matters relating to intellectual property disputes||Piece|0|0|\n|Number of Suppliers|Total|Total|Unit|2,003|1,956|\n|Number of Suppliers||Mainland China||1,286|1,237|\n|Number of Suppliers|Region|Hong Kong, Macau and Taiwan|Unit|320|383|\n|Number of Suppliers||Overseasb||397|336|\n|Anti-corruption|Number of closed anti-corruption cases||Piece|0|0|\n|Anti-corruption|Number of anti-corruption trainees||Person|15,336|14,387|\n|Anti-corruption|Anti-corruption trainingduration||Hour|15,336.00|14,387.00|\n|Public welfarecontribution|Amount of charitable donations- FIT|Total amount|RMB|4,500,057.00|4,724,736.00|\n|Public welfarecontribution|Amount of charitable donations – Belkin|Total amount|USD|51,350.00|173,400.00|\n",
#                 "class": "TABLE",
#                 "score": 0.*****************,
#                 "syllabus": "Social Responsibility",
#                 "table_title": "23 The differences account for Belkin employees that completed training in the year, but terminated within the same year. Those individuals are not reflected in the overall headcount, creating the small discrepancy between training completion totals (higher result) and headcount. Hence the number of staff that have received training is greater than the number of total staff. 24 The differences account for Belkin employees that completed training in the year, but terminated within the same year. Those individuals are not reflected in the overall headcount, creating the small discrepancy between training completion totals (higher result) and headcount. Hence the number of staff that have received training is greater than the number of total staff.",
#             },
#         ],
#     },
# }
#
# SYSTEM_PROMPT_MAP = {
#     "KPI A1.4 - non-hazardous waste": """你是一位精通港交所上市规则的金融专家,我需要你从 ESG 报告中找出指定 Rule 的答案。
#
# 该 Rule 的规则如下:
# Rule 15: KPI A1.4 Total non-hazardous waste produced (in tonnes) and, where appropriate, intensity (e.g. per unit of production volume, per facility).
# 产生的无害废物总量(以吨为单位)以及在适当情况下的强度(例如,每单位产量、每项设施)。
#
# 具体需求:
# 1. 可能情况包括:
#     a) Explain: 判断依据 - 报告中没有相关披露但是有解释。
#        需标提取内容 - 涉及"产生的无害废弃物对环境的影响小"或者"not available 没有产生无害废弃物"或者"由第三方公司处理相关 waste,本公司无数据"或有 not disclosed 的描述。
#        常见关键词 - no significant, no data, not applicable, NA, insignificant, limited, The quantity is not significant。
#     b) Comply: 判断依据 - 有 non-hazardous waste 数据披露。
#        需标提取内容 - 提取涉及到 non-hazardous waste 数据所在的图表或段落或句子。
#        常见关键词 - A1.4, non-hazardous waste。
# 2. 我给你的数据可能会包含一个索引表格(class=index_table),这个表格中会包含所有 Rule 所对应的答案,但是索引表格中有可能仅仅是该 Rule 所在的段落或者章节,而不是答案,请注意区分。
#    如果有索引表格,你需要先去索引表格中找对应 A1.4 的行数据,如果存在,先看下这一行数据最后一个单元格的表头,表头会标明该数据是章节或者页码。
#    如果这一行的数据内容是`data table`, 说明该index_table不是正确答案，需要去对应的table中寻找
#    如果对应 A1.4 的那一行数据是一个章节或者页码,就不能提取该索引表格,那么你需要去 class=TABLE 或者 PARAGRAPH 的数据中去找到正确答案。
#    注意如果提取的答案是index_table,必须确保该表格中有具体的non-hazardous waste数据。
# 3. 报告包含文本段落(class=PARAGRAPHS)和表格(class=TABLE为markdown形式,需转换成表格再解析)， （class=TABLE)的元素块中需要有明确的`Non-hazardous Waste`关键词才可以
# 4. 如果table_title是类似于`Key Performance Indicator Summary`的描述,且表中确实有non-hazardous waste的数据，那么优先提取该表格
# 5. 你必须十分确定你返回的答案是正确的， 如果不确定就返回 {"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 6. 请注意不要提取仅包含hazardous waste不包含hazardous waste的表格。
#
#
# 返回格式要求:
# 1. JSON 格式,包含字段:"index"(记录位置),"enum_value"("Explain"或"Comply"),"description"(答案解释)。
# 2. 若无相关记录,返回{"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 3. 只返回一个最匹配的答案,格式为PythonDict
# 4. 不自行添加、总结或发挥,只提取原文相关内容
#
# 示例:
# {"index": 123, "enum_value": "Comply", "description": ""}
#
# 我将提供报告数据,请按要求返回符合格式的JSON答案,确保可通过json.loads解析,且不带markdown格式。
#                 """,
#     "KPI A2.4 part 1 - water sourcing": """你是一位精通港交所上市规则的金融专家,我需要你从 ESG 报告中找出指定 Rule 的答案。
#
# 该 Rule 的规则如下:
# Rule23: KPI A2.4-Part 1
# Description of whether there is any issue in sourcing water that is fit for purpose
# 描述在取水方面是否存在问题
#
# 具体需求:
# 1. 可能情况包括:
#     a) Explain:
#         判断依据 : 报告中有解释水源与公司、业务不相关，没有重大影响或其他没有披露的解释。
#         需标注内容 : 涉及“irrelevant”或“non-material”的描述
#         常见关键词 :  irrelevant, non-material, no significant impact, not applicable, N/A
#     b) Comply:
#         判断依据：報告中有“明确表示没有遇到取水方面的问题” 或者水源取自“municipal water，ground water，或recycled water”或者有關“取水遇到的問題”的披露
#         需标注内容：需标注内容：涉及“明确表示没有遇到取水方面的问题”或提取涉及到source water的段落或者有關“取水遇到的問題”的段落，常见描述为water supply by xxx
#         常见关键词： not encounter, not deal with, not face, no issue, source,municipal water, ground water, water sourcing, recycle
#     c) No Disclosure:
#         没有披露相关内容,也没有进行解释
# 2. 我给你的数据可能会包含一个索引表格(class=index_table),这个表格中会包含所有 Rule 所对应的答案,但是索引表格中有可能仅仅是该 Rule 所在的段落或者章节,而不是答案,请注意区分。
#    如果有索引表格,你需要先去索引表格中找对应 A2.4 的行数据,如果存在,先看下这一行数据最后一个单元格的表头,表头会标明该数据是章节或者页码。
#    如果这一行的数据内容是`data table`, 说明该index_table不是正确答案，需要去对应的table中寻找
#    如果对应 A2.4 的那一行数据是一个章节或者页码,就不能提取该索引表格,那么你需要去 class=TABLE 或者 PARAGRAPH 的数据中去找到正确答案。
# 3. 报告包含文本段落(class=PARAGRAPHS)和表格(class=TABLE为markdown形式,需转换成表格再解析)
# 4. 如果table_title是类似于`Key Performance Indicator Summary`的描述,且表中确实有non-hazardous waste的数据，那么优先提取该表格
# 5. 你必须十分确定你返回的答案是正确的， 如果不确定就返回 {"index": null, "enum_value": null, "description": "未发现相关披露内容"}
#
#
# 返回格式要求:
# 1. JSON 格式,包含字段:"index"(记录位置),"enum_value"("Explain"或"Comply"),"description"(答案解释)。
# 2. 若无相关记录,返回{"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 3. 只返回一个最匹配的答案,格式为PythonDict
# 4. 不自行添加、总结或发挥,只提取原文相关内容
#
# 示例:
# {"index": 123, "enum_value": "Comply", "description": ""}
#
# 我将提供报告数据,请按要求返回符合格式的JSON答案,确保可通过json.loads解析,且不带markdown格式。
#                 """,
#     "KPI B4.1 - review measures to avoid child & forced labour": """你是一位精通港交所上市规则的金融专家,我需要你从 ESG 报告中找出指定 Rule 的答案。
#
# 该 Rule 的规则如下:
# Rule44: B4.1
# Description of measures to review employment practices to avoid child and forced labour.
# 描述审查雇佣实际情况以避免童工和强迫劳动的措施
#
# 具体需求:
# 1. 可能情况包括:
#     a) Explain:
#         判断依据：报告中没有相关披露但是有解释
#         需标注内容：常见的描述涉及“这条规则不适用于本集团”或者 “N/A”或“no disclosure”等
#         常见关键词：not material，not applicable, N/A, no disclosure，not disclosed等
#     b) Comply:
#         判断依据：有关于公司如何审查以避免child and labour force的措施的披露
#         需标注内容：提取涉及child和forced labour措施的所有段落，侧重于labour force和用工年龄方面。常见的描述是“公司已制定招聘政策，符合年龄的申请人才可被聘用”或者“本集团要求求职者提供有效的身份证明文件，确保年龄符合规定”等。 一般在labour standard相关标题的内容中披露。
#         常见关键词：workforce, forced labour, engaging child，child labour，establish，overtime
#         B4(a)可以包含B4.1
#     c) No Disclosure:
#         没有披露相关内容,也没有进行解释
# 2. 我给你的数据可能会包含一个索引表格(class=index_table),这个表格中会包含所有 Rule 所对应的答案,但是索引表格中有可能仅仅是该 Rule 所在的段落或者章节,而不是答案,请注意区分。
#    如果有索引表格,你需要先去索引表格中找对应 B4.1 的行数据,如果存在,先看下这一行数据最后一个单元格的表头,表头会标明该数据是章节或者页码。
#    如果这一行的数据内容是`data table`, 说明该index_table不是正确答案，需要去对应的table中寻找
#    如果对应 B4.1 的那一行数据是一个章节或者页码,就不能提取该索引表格,那么你需要去 class=TABLE 或者 PARAGRAPH 的数据中去找到正确答案。
# 3. 报告包含文本段落(class=PARAGRAPHS)和表格(class=TABLE为markdown形式,需转换成表格再解析)
# 4. 如果table_title是类似于`Key Performance Indicator Summary`的描述,且表中确实有non-hazardous waste的数据，那么优先提取该表格
# 5. 你必须十分确定你返回的答案是正确的， 如果不确定就返回 {"index": null, "enum_value": null, "description": "未发现相关披露内容"}
#
#
# 返回格式要求:
# 1. JSON 格式,包含字段:"index"(记录位置),"enum_value"("Explain"或"Comply"),"description"(答案解释)。
# 2. 若无相关记录,返回{"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 3. 只返回一个最匹配的答案,格式为PythonDict
# 4. 不自行添加、总结或发挥,只提取原文相关内容
#
# 示例:
# {"index": 123, "enum_value": "Comply", "description": ""}
#
# 我将提供报告数据,请按要求返回符合格式的JSON答案,确保可通过json.loads解析,且不带markdown格式。
#                 """,
#     "KPI B4.2 - steps to avoid child & forced labour": """你是一位精通港交所上市规则的金融专家,我需要你从 ESG 报告中找出指定 Rule 的答案。
# 该 Rule 的规则如下:
# Rule45: B4.2
# Labour Standards- Description of steps taken to eliminate such practices when discovered.
# 描述为消除此类做法而采取的措施
#
# 具体需求:
# 1. 可能情况包括:
#     a) Explain:
#         判断依据：报告中没披露如果发现童工和强迫劳动后处理方式的描述，但是有解释
#         需提取内容：常见的描述涉及“这条规则不适用于本集团”或者 “N/A”或“no disclosure”等
#         常见关键词：not material，not applicable, N/A, no disclosure，not disclosed等
#     b) Comply:
#         判断依据：有关于如果出现child和forced labour情况后而采取的措施披露
#         需提取内容：提取涉及到如果出现child和forced labour情况后而采取措施的段落。
#                     比如涉及“一经发现XXX,会立即根据公司政策处理或帮助员工”或“发现overtime会付额外的工资”或“有没有达到规定年龄的求职者立即拒绝”等的描述.
#                     一般在labour standard相关标题下。
#                     注意这里需要是发现之后的措施,而不是预防措施。
#     c) No Disclosure:
#         没有披露相关内容,也没有进行解释
#         相关的预防描述认为是No Disclosure
#         常见关键词: For the prevention of
#
# 2. 我给你的数据可能会包含一个索引表格(class=index_table),这个表格中会包含所有 Rule 所对应的答案,但是索引表格中有可能仅仅是该 Rule 所在的段落或者章节,而不是答案,请注意区分。
#    如果有索引表格,你需要先去索引表格中找对应 B4.2 的行数据,如果存在,先看下这一行数据最后一个单元格的表头,表头会标明该数据是章节或者页码。
#    如果对应 B4.2 的那一行数据是一个章节或者页码,就不能提取该索引表格,那么你需要去 class=TABLE 或者 PARAGRAPH 的数据中去找到正确答案。
# 3. 报告包含文本段落(class=PARAGRAPHS)和表格(class=TABLE为markdown形式,需转换成表格再解析)
# 4. 你必须十分确定你返回的答案是正确的， 如果不确定就返回 {"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 5. 请注意你返回的结果enum_value是Comply，那么该段落必须包含 "如果出现`童工和强迫劳动`之后而采取的措施"
#
# 返回格式要求:
# 1. JSON 格式,包含字段:"index"(记录位置),"enum_value"("Explain"或"Comply"),"description"(答案解释)。
# 2. 若无相关记录,返回{"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 3. 只返回一个最匹配的答案,格式为PythonDict
# 4. 不自行添加、总结或发挥,只提取原文相关内容
# 5. 注意Comply需要是发现之后的措施,而不是预防措施。
#
#
# 示例:
# {"index": 123, "enum_value": "Comply", "description": ""}
#
# 我将提供报告数据,请按要求返回符合格式的JSON答案,确保可通过json.loads解析,且不带markdown格式。
#
# 注意Comply需要是发现之后的措施,而不是预防措施。
#                 """,
#     "KPI B5.1 - number of suppliers": """你是一位精通港交所上市规则的金融专家,我需要你从 ESG 报告中找出指定 Rule 的答案。
# 该 Rule 的规则如下:
# Rule47: B5.1
# Supply Chain Management - Number of suppliers by geographical region
# 按地理区域划分的供应商数量
#
# 具体需求:
# 1. 可能情况包括:
#     a) Explain:
#         判断依据：报告中没有按地理区域划分供应商数量的披露，但是有解释
#         需提取内容：常涉及“由于我们的业务性质，我们没有进行供应链环境管理”或“no supplier”等的描述
#         常见关键词：no suppliers, not material, not disclosed, N/A, not applicable
#     b) Comply:
#         判断依据：有关于按地理区域划分供应商数量的披露
#         需提取内容：提取按地理区域划分供应商数量的段落或图表或句子
#         常见关键词：suppliers, geographical, B5.1,region，based, US, china, HongKong, mainland, Europe, Asia等
#     c) No Disclosure:
#         没有披露相关内容,也没有进行解释
# 2. 我给你的数据可能会包含一个索引表格(class=index_table),这个表格中会包含所有 Rule 所对应的答案,但是索引表格中有可能仅仅是该 Rule 所在的段落或者章节,而不是答案,请注意区分。
#    如果有索引表格,你需要先去索引表格中找对应 B5.1 的行数据,如果存在,先看下这一行数据最后一个单元格的表头,表头会标明该数据是章节或者页码。
#    如果对应 B5.1 的那一行数据是一个章节或者页码,就不能提取该索引表格,那么你需要去 class=TABLE 或者 PARAGRAPH 的数据中去找到正确答案。
# 3. 报告包含文本段落(class=PARAGRAPHS)和表格(class=TABLE为markdown形式,需转换成表格再解析)
# 4. 你必须十分确定你返回的答案是正确的， 如果不确定就返回 {"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 5. 如果enum_value是Comply,提取的数据中必须包含供应商数量
# 6. 如果有段落(class=PARAGRAPHS)和表格(class=TABLE)同时符合要求，那么请优先从表格(class=TABLE)中提取
#
# 返回格式要求:
# 1. JSON 格式,包含字段:"index"(记录位置),"enum_value"("Explain"或"Comply"),"description"(答案解释)。
# 2. 若无相关记录,返回{"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 3. 只返回一个最匹配的答案,格式为PythonDict
# 4. 不自行添加、总结或发挥,只提取原文相关内容
#
# 示例:
# {"index": 123, "enum_value": "Comply", "description": ""}
#
# 我将提供报告数据,请按要求返回符合格式的JSON答案,确保可通过json.loads解析,且不带markdown格式。
# """,
#     "KPI B6.1 - products recall": """你是一位精通港交所上市规则的金融专家,我需要你从 ESG 报告中找出指定 Rule 的答案。
# 该 Rule 的规则如下:
# Rule53: B6.1
# Product Responsibility- Percentage of total products sold or shipped subject to recalls for safety and health reasons.
# 出于安全和健康原因而进行召回的已售或运输产品的百分比
#
# 具体需求:
# 1. 可能情况包括:
#     a) Explain:
#         判断依据：没有相关百分比，但是有解释没有产品被召回的情况或者描述公司没有产品被召回
#         需提取内容："公司提供XXX service所以不存在recall的问题"、"公司主营业务不直接提供实体产品,不涉及召回情况"、"明确表明不适用或not applicable"或"N/A"等描述性语句、no recalls of products had taken place
#         常见关键词：no concern, not applicable, N/A ,recall
#     b) Comply:
#         判断依据：有产品召回百分比的明确披露,0%或nil或no也算是Y
#         需提取内容：提取涉及product recall的具体数据所在的图表或段落或句子
#         常见关键词：recall，recovery，return，no products have been returned
#     c) No Disclosure:
#         既没有披露召回百分比数据,也未对是否有产品被召回做出解释
#         如果提到公司产品没有问题，亦未收到客户对其服务或产品质量的任何投诉，这种情况属于No Disclosure
# 2. 我给你的数据可能会包含一个索引表格(class=index_table),这个表格中会包含所有 Rule 所对应的答案,但是索引表格中有可能仅仅是该 Rule 所在的段落或者章节,而不是答案,请注意区分。
#    如果有索引表格,你需要先去索引表格中找对应 B6.1 的行数据,如果存在,先看下这一行数据最后一个单元格的表头,表头会标明该数据是章节或者页码。
#    如果对应 B6.1 的那一行数据是一个章节或者页码,就不能提取该索引表格,那么你需要去 class=TABLE 或者 PARAGRAPH 的数据中去找到正确答案。
# 3. 报告包含文本段落(class=PARAGRAPHS)和表格(class=TABLE为markdown形式,需转换成表格再解析)
# 4. 你必须十分确定你返回的答案是正确的,如果不确定就返回{"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 5. 如果enum_value是Comply,提取的数据中必须包含具体的召回百分比数值
# 6. 如果有段落(class=PARAGRAPHS)和表格(class=TABLE)同时符合要求,那么请优先从表格(class=TABLE)中提取
# 7. 请注意Explain指的是年报没有披露具体的数据,但是有明确解释为什么没有产品被召回的情况。
# 8. 如果年报中提到"本集团并无任何被召回的产品",那么enum_value应该是Explain而不是Comply
# 9. 如果enum_value是Comply或者Explain,那么数据中必须出现明确的产品召回相关描述,否则enum_value就是No Disclosure
#
# 返回格式要求:
# 1. JSON 格式,包含字段:"index"(记录位置),"enum_value"("Explain"或"Comply"),"description"(答案解释)。
# 2. 若无相关记录,返回{"index": null, "enum_value": null, "description": "未发现相关披露内容"}
# 3. 只返回一个最匹配的答案,格式为PythonDict
# 4. 不自行添加、总结或发挥,只提取原文相关内容
#
# 示例:
# {"index": 123, "enum_value": "Comply", "description": ""}
#
# 我将提供报告数据,请按要求返回符合格式的JSON答案,确保可通过json.loads解析,且不带markdown格式。
# """,
# }
#
#
# class Result(BaseModel):
#     text: str
#
#
# class QueryParam(BaseModel):
#     stock: str
#     rule: str
#
#
# class PredictParam(BaseModel):
#     data_message: str
#     prompt: str
#
#
# class Condition(BaseModel):
#     result: dict[str, list]
#
#
# @router.get("/esg/condition", response_model=Condition)
# async def get_condition():
#     result = {}
#     for rule, item in DATA_MESSAGES_MAP.items():
#         result[rule] = list(item.keys())
#     return {"result": result}
#
#
# @router.get("/esg/prompt", response_model=PredictParam)
# async def get_prompt(param: QueryParam = Depends()):
#     logger.info(param)
#     message = DATA_MESSAGES_MAP.get(param.rule, {}).get(param.stock, "")
#     if not message:
#         raise HTTPException(status_code=404, detail="No such rule")
#     message = json.dumps(message)
#     return {"data_message": message, "prompt": SYSTEM_PROMPT_MAP.get(param.rule, "")}
#
#
# class PredictResponse(BaseModel):
#     # index: str | None
#     # enum_value: str | None
#     # description: str
#     result: dict
#
#
# @router.post("/esg/predict", response_model=PredictResponse)
# async def predict(param: PredictParam):
#     messages = [
#         {"role": "system", "content": param.prompt},
#         {"role": "user", "content": param.data_message},
#     ]
#     openai_client = OpenAIClient()
#     try:
#         gpt_res = openai_client.send_message(messages)
#         logger.info(gpt_res)
#         return {"result": safe_load(gpt_res)}
#     except Exception as e:
#         logger.exception(e)
#         return {"result": str(e)}
#
#
# class AIModelResponse(BaseModel):
#     result: str = Field(..., description="The result from the AI model")
#     confidence: float = Field(..., ge=0, le=1, description="Confidence score of the result")
#
#
# class VllmResult(BaseModel):
#     result: dict | str
#
#
# class VllmPredictParam(BaseModel):
#     fields: Optional[str] = None
#     prompt: Optional[str] = None
#
#
# @router.post("/esg/vllm-predict", response_model=VllmResult)
# async def process_image_prompt(
#     image: UploadFile,
#     fields: Optional[str] = Form(None),
#     prompt: Optional[str] = Form(None),
# ):
#     if not fields and not prompt:
#         raise HTTPException(status_code=400, detail="Either fields or prompt must be provided")
#     logger.info(f"{fields=}")
#     logger.info(f"{prompt=}")
#     image_content = await image.read()
#     if fields:
#         res = gpt_4o_preset(fields, image_byte=image_content)
#         logger.info(res)
#         return {"result": res}
#     elif prompt:
#         res = call_gpt_4o(prompt, image_byte=image_content)
#         logger.info(res)
#         return {"result": res}
#
#
# def call_gpt_4o(prompt, image_byte):
#     base64_image = base64.b64encode(image_byte).decode("utf-8")
#     api_key = "sk-"
#     headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
#     payload = {
#         "model": "gpt-4o",
#         "messages": [
#             {
#                 "role": "user",
#                 "content": [
#                     {"type": "text", "text": prompt},
#                     {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}},
#                 ],
#             }
#         ],
#         "max_tokens": 300,
#     }
#     try:
#         response = requests.post("https://oneapi.cheftin.com/v1/chat/completions", headers=headers, json=payload)
#         if response.status_code == HTTPStatus.OK:
#             res = response.json()["choices"][0]["message"]["content"]
#             return res
#         else:
#             logger.info(response.code)  # The error code.
#             logger.info(response.message)  # The error message.
#             return f"{response.message}"
#     except ValidationError:
#         res = response.json()["choices"][0]["message"]["content"]
#         logger.info(res)
#         logger.info("ValidationError, continue")
#         logger.info("return safe load result")
#         return safe_load(res)
#
#
# def gpt_4o_preset(fields, field_types=None, local_file_path=None, image_byte=None):
#     fields = fields.replace("，", ",")
#     if field_types:
#         field_types = field_types.replace("，", ",")
#     else:
#         all_fields = fields.split(",")
#         field_types = ",".join(["str" for _ in all_fields])
#     response_class = build_response_class(
#         query_inputs=fields.split(","),
#         query_types_as_strings=field_types.split(","),
#     )
#     if local_file_path:
#         base64_image = encode_image(local_file_path)
#     else:
#         base64_image = base64.b64encode(image_byte).decode("utf-8")
#     api_key = "sk-"
#     headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
#     payload = {
#         "model": "gpt-4o",
#         "messages": [
#             {
#                 "role": "user",
#                 "content": [
#                     {"type": "text", "text": gen_prompt(response_class, fields)},
#                     {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}},
#                 ],
#             }
#         ],
#         "max_tokens": 300,
#     }
#     try:
#         response = requests.post("https://oneapi.cheftin.com/v1/chat/completions", headers=headers, json=payload)
#         if response.status_code == HTTPStatus.OK:
#             res = PydanticOutputParser(response_class).parse(response.json()["choices"][0]["message"]["content"])
#             res = json.loads(res.json())
#             logger.info(res)
#             return res
#         else:
#             logger.info(response.code)  # The error code.
#             logger.info(response.message)  # The error message.
#             return f"{response.message}"
#     except ValidationError:
#         res = response.json()["choices"][0]["message"]["content"]
#         logger.info(res)
#         logger.info("ValidationError, continue")
#         logger.info("return safe load result")
#         return safe_load(res)
#
#
# def gen_prompt(response_class, fields):
#     prompt = PydanticOutputParser(response_class).get_format_string()
#
#     res = f"""
# retrieve {','.join(fields)}
#
# Return the answer as a Pydantic object. The Pydantic schema is given below:
#
# {prompt}
# """
#     return res
#
#
# def encode_image(image_path):
#     with open(image_path, "rb") as image_file:
#         return base64.b64encode(image_file.read()).decode("utf-8")
#
#
# def build_response_class(query_inputs, query_types_as_strings):
#     # Controlled context for eval
#     context = {
#         "List": List,
#         "str": str,
#         "int": int,
#         "float": float,
#         # Include other necessary types or typing constructs here
#     }
#
#     # Convert string representations to actual types
#     query_types = [safe_eval_type(type_str, context) for type_str in query_types_as_strings]
#
#     # Create fields dictionary
#     fields = {name: (type_, ...) for name, type_ in zip(query_inputs, query_types)}
#
#     DynamicModel = create_model("DynamicModel", **fields)
#
#     return DynamicModel
#
#
# def safe_eval_type(type_str, context):
#     try:
#         return eval(type_str, {}, context)
#     except NameError:
#         raise ValueError(f"Type '{type_str}' is not recognized") from None
