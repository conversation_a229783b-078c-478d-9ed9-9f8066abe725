import pandas as pd
import numpy as np
import matplotlib.pyplot as plt


def demonstrate_rrf_with_different_k(ranks, k_values):
    """
    Demonstrate how different k values affect RRF scores
    """
    scores = {}
    for k in k_values:
        scores[k] = [1 / (rank + k) for rank in ranks]

    # Create comparison DataFrame
    df = pd.DataFrame(scores, index=ranks)
    df.index.name = "Rank"
    return df


def plot_rrf_scores(ranks, k_values):
    """
    Plot RRF scores for different k values
    """
    plt.figure(figsize=(12, 6))
    for k in k_values:
        scores = [1 / (rank + k) for rank in ranks]
        plt.plot(ranks, scores, label=f"k={k}", marker="o")

    plt.title("RRF Scores vs Rank for Different k Values")
    plt.xlabel("Rank Position")
    plt.ylabel("RRF Score")
    plt.grid(True)
    plt.legend()
    return plt


# Example implementation
ranks = range(10)  # Ranks 0-9
k_values = [1, 10, 60, 100]

# Calculate scores
scores_df = demonstrate_rrf_with_different_k(ranks, k_values)

print("RRF Scores for different k values:")
print(scores_df)


# Real-world example with multiple queries
def rrf_example():
    # Example search results (document: rank)
    query1_results = {"doc1": 1, "doc2": 2, "doc3": 3}
    query2_results = {"doc2": 1, "doc1": 2, "doc4": 3}
    query3_results = {"doc3": 1, "doc1": 2, "doc2": 3}

    all_queries = {"query1": query1_results, "query2": query2_results, "query3": query3_results}

    k = 60  # Standard k value

    # Calculate RRF scores
    rrf_scores = {}
    for query_name, rankings in all_queries.items():
        print(f"\nProcessing {query_name}:")
        for doc, rank in rankings.items():
            if doc not in rrf_scores:
                rrf_scores[doc] = 0
            score = 1 / (rank + k)
            rrf_scores[doc] += score
            print(f"Document {doc} at rank {rank}: Adding score {score:.4f}")

    # Sort by final score
    final_rankings = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)
    return final_rankings


print("\nReal-world RRF Example:")
final_rankings = rrf_example()
print("\nFinal Rankings:")
for doc, score in final_rankings:
    print(f"Document {doc}: {score:.4f}")
