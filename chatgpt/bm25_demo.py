from rank_bm25 import BM25Okapi
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords

# 下载必要的NLTK数据
nltk.download("punkt")
nltk.download("stopwords")


def preprocess(text):
    # 分词
    tokens = word_tokenize(text.lower())
    # 去除停用词
    stop_words = set(stopwords.words("english"))
    return [token for token in tokens if token not in stop_words]


def create_corpus(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        # 假设每行是一个文档
        return [line.strip() for line in file]


def bm25_retrieval(corpus, query, top_k=5):
    # 创建BM25模型
    bm25 = BM25Okapi(corpus)

    # 对查询进行预处理
    processed_query = preprocess(query)

    # 计算文档得分
    doc_scores = bm25.get_scores(processed_query)

    # 获取前top_k个最相关的文档
    top_docs = sorted(range(len(doc_scores)), key=lambda i: doc_scores[i], reverse=True)[:top_k]

    return top_docs


# 使用示例
if __name__ == "__main__":
    # file_path = "/Users/<USER>/workspace/xx/data/60904.txt"
    file_path = "/Users/<USER>/workspace/xx/data/67845.txt"
    data = create_corpus(file_path)
    corpus = [preprocess(line.strip()) for line in data]

    query = "the reasons for making the issue"
    top_k = 20
    results = bm25_retrieval(corpus, query, top_k)

    print(f"Top {top_k} relevant documents:")
    for i, doc_id in enumerate(results, 1):
        print(f"{i}. Document {doc_id} {data[doc_id]}")
